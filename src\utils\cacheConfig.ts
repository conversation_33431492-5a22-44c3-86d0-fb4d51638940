/**
 * Configuração centralizada de cache para React Query
 * Otimizada para performance e experiência do usuário
 */

export const CACHE_TIMES = {
  // Cache muito longo para dados que raramente mudam
  STATIC: {
    staleTime: 24 * 60 * 60 * 1000, // 24 horas
    gcTime: 7 * 24 * 60 * 60 * 1000, // 7 dias
  },

  // Cache longo para dados que mudam pouco
  LONG: {
    staleTime: 60 * 60 * 1000, // 1 hora
    gcTime: 24 * 60 * 60 * 1000, // 24 horas
  },

  // Cache médio para dados que mudam moderadamente
  MEDIUM: {
    staleTime: 15 * 60 * 1000, // 15 minutos
    gcTime: 60 * 60 * 1000, // 1 hora
  },

  // Cache curto para dados que mudam frequentemente
  SHORT: {
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 15 * 60 * 1000, // 15 minutos
  },

  // Cache muito curto para dados em tempo real
  REALTIME: {
    staleTime: 30 * 1000, // 30 segundos
    gcTime: 2 * 60 * 1000, // 2 minutos
  },

  // Cache infinito para dados que nunca mudam
  INFINITE: {
    staleTime: Infinity,
    gcTime: Infinity,
  }
} as const;

/**
 * Configurações específicas por tipo de dados
 */
export const CACHE_STRATEGIES = {
  // Dados do usuário (perfil, configurações)
  USER_DATA: {
    ...CACHE_TIMES.MEDIUM,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: true, // Atualizar ao reconectar
    retry: 2,
  },

  // Medicamentos e doses (dados críticos) - Cache otimizado para performance
  MEDICATIONS: {
    ...CACHE_TIMES.MEDIUM, // 15 minutos - mais conservador
    refetchOnWindowFocus: true, // Verificar ao focar para detectar problemas
    refetchOnMount: false, // Não verificar ao montar (usa cache)
    refetchOnReconnect: true, // Verificar ao reconectar (importante)
    retry: 3, // Mais tentativas para recuperar de erros
    retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 5000),
    networkMode: 'online' as const,
  },

  // Cache específico para cálculos de dose (ainda mais curto)
  MEDICATION_CALCULATIONS: {
    staleTime: 2 * 60 * 1000, // 2 minutos apenas
    gcTime: 5 * 60 * 1000, // 5 minutos
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    refetchOnReconnect: true,
    retry: 3,
  },

  // Questões e estudos
  QUESTIONS: {
    ...CACHE_TIMES.MEDIUM,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: 1,
  },

  // Busca e filtros
  SEARCH: {
    ...CACHE_TIMES.LONG,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: 1,
  },

  // Configurações do site
  SITE_SETTINGS: {
    ...CACHE_TIMES.STATIC,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: true,
    retry: 2,
  },

  // Dados de análise e estatísticas
  ANALYTICS: {
    ...CACHE_TIMES.SHORT,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: 1,
  },

  // Dados locais (idade, peso, etc.)
  LOCAL_DATA: {
    ...CACHE_TIMES.INFINITE,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: 0,
  },

  // Medicamentos de amamentação - estrutura completa (view materializada)
  BREASTFEEDING_STRUCTURE: {
    staleTime: 60 * 60 * 1000, // 1 hora (view materializada é rápida)
    gcTime: 2 * 60 * 60 * 1000, // 2 horas
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: true,
    retry: 2,
  },

  // Seções de amamentação (dados muito estáticos)
  BREASTFEEDING_SECTIONS: {
    ...CACHE_TIMES.STATIC,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: 1,
  },

  // Subseções de amamentação
  BREASTFEEDING_SUBSECTIONS: {
    staleTime: 12 * 60 * 60 * 1000, // 12 horas
    gcTime: 24 * 60 * 60 * 1000, // 24 horas
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: 1,
  },

  // Medicamentos específicos de amamentação
  BREASTFEEDING_MEDICATIONS: {
    staleTime: 6 * 60 * 60 * 1000, // 6 horas
    gcTime: 12 * 60 * 60 * 1000, // 12 horas
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: true,
    retry: 1,
  },

  // Busca de medicamentos de amamentação
  BREASTFEEDING_SEARCH: {
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: 2,
  }
} as const;

/**
 * Chaves de cache padronizadas
 */
export const CACHE_KEYS = {
  USER_PROFILE: (userId: string) => ['user-profile', userId],
  USER_STATS: (userId: string) => ['user-stats', userId],
  MEDICATIONS: ['medications'],
  MEDICATION_DETAIL: (slug: string) => ['medication', slug],
  MEDICATION_TAGS: (medicationId: string) => ['medication-tags', medicationId],
  MEDICATION_CATEGORIES: ['medication-categories'],
  QUESTIONS: (domain: string, filters: any) => ['questions', domain, filters],
  QUESTION_COUNT: (domain: string, filters: any) => ['question-count', domain, filters],
  SEARCH_RESULTS: (term: string) => ['search', term],
  SITE_SETTINGS: ['site-settings'],
  CATEGORIES: ['categories'],
  AGE: ['age'],
  WEIGHT: ['weight'],
  GROQ_ANALYSIS: (userId: string) => ['groq-analysis', userId],

  // Chaves para medicamentos de amamentação
  BREASTFEEDING_STRUCTURE: ['breastfeeding-structure'],
  BREASTFEEDING_SECTIONS: ['breastfeeding-sections'],
  BREASTFEEDING_SUBSECTIONS: (sectionId: string) => ['breastfeeding-subsections', sectionId],
  BREASTFEEDING_MEDICATIONS: (subsectionId: string) => ['breastfeeding-medications', subsectionId],
  BREASTFEEDING_SEARCH: (query: string) => ['breastfeeding-search', query],
} as const;

/**
 * Configuração padrão otimizada para React Query
 */
export const DEFAULT_QUERY_CONFIG = {
  defaultOptions: {
    queries: {
      ...CACHE_TIMES.MEDIUM,
      refetchOnWindowFocus: true, // Detectar problemas ao focar
      refetchOnMount: false,
      refetchOnReconnect: true, // Importante para recuperar de timeouts
      retry: 3, // Mais tentativas
      retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 10000),
      // Configurações de rede otimizadas
      networkMode: 'online' as const,
      // Configurações de erro
      throwOnError: false,
      // Timeout para queries
      meta: {
        timeout: 30000, // 30 segundos timeout
      }
    },
    mutations: {
      retry: 2, // Mais tentativas para mutations
      retryDelay: 1500,
      networkMode: 'online' as const,
      meta: {
        timeout: 15000, // 15 segundos timeout para mutations
      }
    }
  }
} as const;

/**
 * Utilitários para invalidação de cache
 */
export const CACHE_INVALIDATION = {
  // Invalidar cache relacionado ao usuário
  USER_RELATED: (userId: string) => [
    CACHE_KEYS.USER_PROFILE(userId),
    CACHE_KEYS.USER_STATS(userId),
    CACHE_KEYS.GROQ_ANALYSIS(userId),
  ],

  // Invalidar cache de questões
  QUESTIONS_RELATED: (domain: string) => [
    ['questions', domain],
    ['question-count', domain],
  ],

  // Invalidar cache de medicamentos
  MEDICATIONS_RELATED: () => [
    CACHE_KEYS.MEDICATIONS,
    ['medication'],
  ],

  // Invalidar tudo (usar com cuidado)
  ALL: () => ['*']
} as const;
