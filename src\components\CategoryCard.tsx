import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { LucideIcon, Sparkles } from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import AuthDialog from "@/components/auth/AuthDialog";
import { useUser } from "@supabase/auth-helpers-react";

interface CategoryCardProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  color: string;
  path: string;
  imageUrl?: string;
  badge?: string;
  onClick?: () => void;
  hideDescription?: boolean;
  comingSoon?: boolean;
  requiresAuth?: boolean;
  showNewBadge?: boolean;
  customBadgeText?: string;
  customBadgeColor?: string;
  showFeatureBadges?: boolean;
  isSpecial?: boolean;
  specialGlow?: boolean;
  longTitle?: boolean;
}

const CategoryCard = ({
  title,
  description,
  icon: Icon,
  color,
  path,
  imageUrl,
  badge,
  onClick,
  hideDescription,
  comingSoon,
  requiresAuth,
  showNewBadge,
  customBadgeText,
  customBadgeColor,
  showFeatureBadges,
  isSpecial,
  specialGlow,
  longTitle,
}: CategoryCardProps) => {
  const navigate = useNavigate();
  const user = useUser();
  const [showAuthDialog, setShowAuthDialog] = useState(false);

  // Extract color name from the color class (e.g., "bg-amber-50" -> "amber")
  const colorName = color.split('-')[1];

  // Enhanced border styles with matching color - 2px border for visibility
  const borderStyle =
    color.includes("amber") ? "border-amber-500 dark:border-amber-600" :
    color.includes("emerald") ? "border-emerald-500 dark:border-emerald-600" :
    color.includes("purple") ? "border-purple-500 dark:border-purple-600" :
    color.includes("blue") ? "border-blue-500 dark:border-blue-600" :
    color.includes("red") ? "border-red-500 dark:border-red-600" :
    color.includes("cyan") ? "border-cyan-500 dark:border-cyan-600" :
    color.includes("green") ? "border-green-500 dark:border-green-600" :
    color.includes("yellow") ? "border-yellow-500 dark:border-yellow-600" :
    color.includes("indigo") ? "border-indigo-500 dark:border-indigo-600" :
    color.includes("rose") ? "border-rose-500 dark:border-rose-600" :
    color.includes("orange") ? "border-orange-500 dark:border-orange-600" :
    color.includes("pink") ? "border-pink-500 dark:border-pink-600" :
    color.includes("violet") ? "border-violet-500 dark:border-violet-600" :
    color.includes("teal") ? "border-teal-500 dark:border-teal-600" :
    color.includes("sky") ? "border-sky-500 dark:border-sky-600" :
    color.includes("fuchsia") ? "border-fuchsia-500 dark:border-fuchsia-600" :
    color.includes("lime") ? "border-lime-500 dark:border-lime-600" :
    "border-gray-500 dark:border-gray-600";

  // Dark mode colors for icon backgrounds
  const iconBgStyle =
    color.includes("amber") ? "bg-amber-50" :
    color.includes("emerald") ? "bg-emerald-50" :
    color.includes("purple") ? "bg-purple-50" :
    color.includes("blue") ? "bg-blue-50" :
    color.includes("red") ? "bg-red-50" :
    color.includes("cyan") ? "bg-cyan-50" :
    color.includes("green") ? "bg-green-50" :
    color.includes("yellow") ? "bg-yellow-50" :
    color.includes("indigo") ? "bg-indigo-50" :
    color.includes("rose") ? "bg-rose-50" :
    color.includes("orange") ? "bg-orange-50" :
    color.includes("pink") ? "bg-pink-50" :
    color.includes("violet") ? "bg-violet-50" :
    color.includes("teal") ? "bg-teal-50" :
    color.includes("sky") ? "bg-sky-50" :
    color.includes("fuchsia") ? "bg-fuchsia-50" :
    color.includes("lime") ? "bg-lime-50" :
    "bg-white";

  // Dark mode colors for icon backgrounds
  const iconDarkBgStyle =
    color.includes("amber") ? "dark:bg-amber-900/30" :
    color.includes("emerald") ? "dark:bg-emerald-900/30" :
    color.includes("purple") ? "dark:bg-purple-900/30" :
    color.includes("blue") ? "dark:bg-blue-900/30" :
    color.includes("red") ? "dark:bg-red-900/30" :
    color.includes("cyan") ? "dark:bg-cyan-900/30" :
    color.includes("green") ? "dark:bg-green-900/30" :
    color.includes("yellow") ? "dark:bg-yellow-900/30" :
    color.includes("indigo") ? "dark:bg-indigo-900/30" :
    color.includes("rose") ? "dark:bg-rose-900/30" :
    color.includes("orange") ? "dark:bg-orange-900/30" :
    color.includes("pink") ? "dark:bg-pink-900/30" :
    color.includes("violet") ? "dark:bg-violet-900/30" :
    color.includes("teal") ? "dark:bg-teal-900/30" :
    color.includes("sky") ? "dark:bg-sky-900/30" :
    color.includes("fuchsia") ? "dark:bg-fuchsia-900/30" :
    color.includes("lime") ? "dark:bg-lime-900/30" :
    "dark:bg-slate-700/50";

  // Dark mode colors for icon text
  const iconTextStyle =
    color.includes("amber") ? "text-amber-700" :
    color.includes("emerald") ? "text-emerald-700" :
    color.includes("purple") ? "text-purple-700" :
    color.includes("blue") ? "text-blue-700" :
    color.includes("red") ? "text-red-700" :
    color.includes("cyan") ? "text-cyan-700" :
    color.includes("green") ? "text-green-700" :
    color.includes("yellow") ? "text-yellow-700" :
    color.includes("indigo") ? "text-indigo-700" :
    color.includes("rose") ? "text-rose-700" :
    color.includes("orange") ? "text-orange-700" :
    color.includes("pink") ? "text-pink-700" :
    color.includes("violet") ? "text-violet-700" :
    color.includes("teal") ? "text-teal-700" :
    color.includes("sky") ? "text-sky-700" :
    color.includes("fuchsia") ? "text-fuchsia-700" :
    color.includes("lime") ? "text-lime-700" :
    "text-gray-700";

  // Dark mode colors for icon text
  const iconDarkTextStyle =
    color.includes("amber") ? "dark:text-amber-300" :
    color.includes("emerald") ? "dark:text-emerald-300" :
    color.includes("purple") ? "dark:text-purple-300" :
    color.includes("blue") ? "dark:text-blue-300" :
    color.includes("red") ? "dark:text-red-300" :
    color.includes("cyan") ? "dark:text-cyan-300" :
    color.includes("green") ? "dark:text-green-300" :
    color.includes("yellow") ? "dark:text-yellow-300" :
    color.includes("indigo") ? "dark:text-indigo-300" :
    color.includes("rose") ? "dark:text-rose-300" :
    color.includes("orange") ? "dark:text-orange-300" :
    color.includes("pink") ? "dark:text-pink-300" :
    color.includes("violet") ? "dark:text-violet-300" :
    color.includes("teal") ? "dark:text-teal-300" :
    color.includes("sky") ? "dark:text-sky-300" :
    color.includes("fuchsia") ? "dark:text-fuchsia-300" :
    color.includes("lime") ? "dark:text-lime-300" :
    "dark:text-gray-300";

  const handleCardClick = () => {
    if (requiresAuth && !user) {
      setShowAuthDialog(true);
      return;
    }

    if (comingSoon) {
      return;
    }

    if (onClick) {
      onClick();
      return;
    }

    navigate(path);
  };

  return (
    <>
      <div
        className={cn(
          "relative h-full p-4 sm:p-5 rounded-xl transition-all duration-300 cursor-pointer",
          // Estilo mais moderno com sombra e fundo de vidro para aparência de app
          "bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-md hover:shadow-lg",
          // Borda mais sutil para aparência de app
          "border border-gray-100 dark:border-gray-700/50",
          // Efeito de hover mais suave
          "hover:-translate-y-1",
          // Efeitos especiais para cards especiais
          isSpecial && "ring-2 ring-pink-300/50 dark:ring-pink-500/30",
          specialGlow && "shadow-pink-200/50 dark:shadow-pink-500/20 shadow-xl",
          specialGlow && "hover:shadow-pink-300/60 dark:hover:shadow-pink-400/30 hover:shadow-2xl",
          comingSoon && "opacity-75 cursor-not-allowed"
        )}
        onClick={handleCardClick}
        style={specialGlow ? {
          background: color.includes("amber") || color.includes("yellow") ?
            'linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.05) 50%, rgba(217, 119, 6, 0.1) 100%)' :
          color.includes("emerald") || color.includes("green") ?
            'linear-gradient(135deg, rgba(52, 211, 153, 0.1) 0%, rgba(16, 185, 129, 0.05) 50%, rgba(5, 150, 105, 0.1) 100%)' :
          color.includes("purple") || color.includes("violet") ?
            'linear-gradient(135deg, rgba(168, 85, 247, 0.1) 0%, rgba(139, 92, 246, 0.05) 50%, rgba(124, 58, 237, 0.1) 100%)' :
          color.includes("orange") ?
            'linear-gradient(135deg, rgba(251, 146, 60, 0.1) 0%, rgba(249, 115, 22, 0.05) 50%, rgba(234, 88, 12, 0.1) 100%)' :
          color.includes("indigo") || color.includes("blue") ?
            'linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(59, 130, 246, 0.05) 50%, rgba(37, 99, 235, 0.1) 100%)' :
          color.includes("red") || color.includes("rose") ?
            'linear-gradient(135deg, rgba(248, 113, 113, 0.1) 0%, rgba(239, 68, 68, 0.05) 50%, rgba(220, 38, 38, 0.1) 100%)' :
          color.includes("cyan") || color.includes("teal") ?
            'linear-gradient(135deg, rgba(34, 211, 238, 0.1) 0%, rgba(6, 182, 212, 0.05) 50%, rgba(8, 145, 178, 0.1) 100%)' :
          color.includes("pink") ?
            'linear-gradient(135deg, rgba(251, 207, 232, 0.1) 0%, rgba(244, 114, 182, 0.05) 50%, rgba(236, 72, 153, 0.1) 100%)' :
          'linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.05) 50%, rgba(29, 78, 216, 0.1) 100%)',
          boxShadow: isSpecial ? (
            color.includes("amber") || color.includes("yellow") ? '0 0 30px rgba(217, 119, 6, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)' :
            color.includes("emerald") || color.includes("green") ? '0 0 30px rgba(5, 150, 105, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)' :
            color.includes("purple") || color.includes("violet") ? '0 0 30px rgba(124, 58, 237, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)' :
            color.includes("orange") ? '0 0 30px rgba(234, 88, 12, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)' :
            color.includes("indigo") || color.includes("blue") ? '0 0 30px rgba(37, 99, 235, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)' :
            color.includes("red") || color.includes("rose") ? '0 0 30px rgba(220, 38, 38, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)' :
            color.includes("cyan") || color.includes("teal") ? '0 0 30px rgba(8, 145, 178, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)' :
            color.includes("pink") ? '0 0 30px rgba(236, 72, 153, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)' :
            '0 0 30px rgba(29, 78, 216, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)'
          ) : undefined
        } : undefined}
      >
        {/* Barra de cor na parte superior para aparência de app */}
        <div className={cn(
          "absolute top-0 left-0 right-0 rounded-t-xl",
          isSpecial ? "h-2" : "h-1.5",
          isSpecial ? (
            color.includes("amber") || color.includes("yellow") ? "bg-gradient-to-r from-amber-400 via-yellow-400 to-amber-500" :
            color.includes("emerald") || color.includes("green") ? "bg-gradient-to-r from-emerald-400 via-green-400 to-emerald-500" :
            color.includes("purple") || color.includes("violet") ? "bg-gradient-to-r from-purple-400 via-violet-400 to-purple-500" :
            color.includes("orange") ? "bg-gradient-to-r from-orange-400 via-amber-400 to-orange-500" :
            color.includes("indigo") || color.includes("blue") ? "bg-gradient-to-r from-indigo-400 via-blue-400 to-indigo-500" :
            color.includes("red") || color.includes("rose") ? "bg-gradient-to-r from-red-400 via-rose-400 to-red-500" :
            color.includes("cyan") || color.includes("teal") ? "bg-gradient-to-r from-cyan-400 via-teal-400 to-cyan-500" :
            color.includes("pink") ? "bg-gradient-to-r from-pink-400 via-rose-400 to-pink-500" :
            "bg-gradient-to-r from-primary via-blue-500 to-primary"
          ) : (
            color.includes("amber") ? "bg-amber-500" :
            color.includes("emerald") ? "bg-emerald-500" :
            color.includes("purple") ? "bg-purple-500" :
            color.includes("blue") ? "bg-blue-500" :
            color.includes("red") ? "bg-red-500" :
            color.includes("cyan") ? "bg-cyan-500" :
            color.includes("green") ? "bg-green-500" :
            color.includes("yellow") ? "bg-yellow-500" :
            color.includes("indigo") ? "bg-indigo-500" :
            color.includes("rose") ? "bg-rose-500" :
            "bg-primary"
          ),
          isSpecial && (
            color.includes("amber") || color.includes("yellow") ? "shadow-lg shadow-amber-200/50" :
            color.includes("emerald") || color.includes("green") ? "shadow-lg shadow-emerald-200/50" :
            color.includes("purple") || color.includes("violet") ? "shadow-lg shadow-purple-200/50" :
            color.includes("orange") ? "shadow-lg shadow-orange-200/50" :
            color.includes("indigo") || color.includes("blue") ? "shadow-lg shadow-indigo-200/50" :
            color.includes("red") || color.includes("rose") ? "shadow-lg shadow-red-200/50" :
            color.includes("cyan") || color.includes("teal") ? "shadow-lg shadow-cyan-200/50" :
            color.includes("pink") ? "shadow-lg shadow-pink-200/50" :
            "shadow-lg shadow-blue-200/50"
          )
        )} />

        {/* New Badge - Estilo mais moderno */}
        {showNewBadge && (
          <div className="absolute -top-2 -right-2 z-10">
            <div className={cn(
              "px-2 py-0.5 text-white text-xs font-medium rounded-md shadow-md flex items-center gap-1",
              isSpecial ? (
                color.includes("amber") || color.includes("yellow") ? "bg-gradient-to-r from-amber-500 to-yellow-500 shadow-amber-300/50" :
                color.includes("emerald") || color.includes("green") ? "bg-gradient-to-r from-emerald-500 to-green-500 shadow-emerald-300/50" :
                color.includes("purple") || color.includes("violet") ? "bg-gradient-to-r from-purple-500 to-violet-500 shadow-purple-300/50" :
                color.includes("orange") ? "bg-gradient-to-r from-orange-500 to-amber-500 shadow-orange-300/50" :
                color.includes("indigo") || color.includes("blue") ? "bg-gradient-to-r from-indigo-500 to-blue-500 shadow-indigo-300/50" :
                color.includes("red") || color.includes("rose") ? "bg-gradient-to-r from-red-500 to-rose-500 shadow-red-300/50" :
                color.includes("cyan") || color.includes("teal") ? "bg-gradient-to-r from-cyan-500 to-teal-500 shadow-cyan-300/50" :
                color.includes("pink") ? "bg-gradient-to-r from-pink-500 to-rose-500 shadow-pink-300/50" :
                "bg-gradient-to-r from-blue-500 to-indigo-500 shadow-blue-300/50"
              ) : (
                customBadgeColor ? `bg-${customBadgeColor}-500` : "bg-red-500"
              )
            )}>
              {isSpecial && <Sparkles className="w-3 h-3" />}
              {customBadgeText || "Novo"}
            </div>
          </div>
        )}

        <div className="flex flex-col items-center text-center h-full justify-start pt-2 space-y-2">
          {imageUrl ? (
            <div className="w-16 h-16 sm:w-20 sm:h-20 rounded-lg overflow-hidden shadow-md">
              <img
                src={imageUrl}
                alt={title}
                className="w-full h-full object-cover"
              />
            </div>
          ) : Icon && (
            <div className={cn(
              "w-14 h-14 sm:w-16 sm:h-16 rounded-lg flex items-center justify-center shadow-sm",
              iconBgStyle,
              iconDarkBgStyle,
            )}>
              <Icon className={cn(
                "w-7 h-7 sm:w-8 sm:h-8",
                iconTextStyle,
                iconDarkTextStyle
              )} />
            </div>
          )}

          <h3 className={cn(
            "font-bold text-gray-800 dark:text-gray-200",
            // Usar propriedade longTitle para títulos que precisam de mais espaço (mantendo tamanho original)
            longTitle ? "line-clamp-3 leading-tight" : "line-clamp-2",
            hideDescription ? "text-lg sm:text-xl" : "text-sm sm:text-base md:text-lg"
          )}>
            {title}
          </h3>

          {!hideDescription && (
            <p className="hidden sm:block text-xs sm:text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mt-1.5 min-h-[32px]">
              {description || " "}
            </p>
          )}

          {/* Espaçador flexível para empurrar badges para baixo */}
          <div className="flex-1"></div>

          {/* Feature badges - Ocultas no mobile */}
          {showFeatureBadges && (
            <div className="hidden sm:flex flex-wrap gap-1 justify-center">
              <div className={cn(
                "flex items-center gap-1 bg-gray-100/80 dark:bg-slate-700/80",
                "text-[10px] px-1.5 py-0.5 rounded-md",
                color.includes("orange") ? "text-orange-700 dark:text-orange-400" :
                color.includes("amber") ? "text-amber-700 dark:text-amber-400" :
                "text-primary dark:text-blue-400"
              )}>
                <Sparkles className="w-2.5 h-2.5" />
                <span>Todas especialidades</span>
              </div>
            </div>
          )}

          {/* Regular badges - Ocultas no mobile */}
          {(badge || comingSoon) && (
            <div className="hidden sm:flex gap-1.5 flex-wrap justify-center">
              {badge && (
                <Badge variant="outline" className="bg-gray-100/80 dark:bg-slate-700/80 border-none text-gray-700 dark:text-gray-300 text-[10px] font-medium px-2 py-0.5 h-auto">
                  {badge}
                </Badge>
              )}
              {comingSoon && (
                <Badge variant="outline" className="bg-yellow-100/80 dark:bg-yellow-900/30 border-none text-yellow-700 dark:text-yellow-400 text-[10px] font-medium px-2 py-0.5 h-auto">
                  Em breve
                </Badge>
              )}
            </div>
          )}
        </div>
      </div>

      {requiresAuth && (
        <AuthDialog
          open={showAuthDialog}
          onOpenChange={setShowAuthDialog}
          hidden={true}
        />
      )}
    </>
  );
};

export default CategoryCard;
