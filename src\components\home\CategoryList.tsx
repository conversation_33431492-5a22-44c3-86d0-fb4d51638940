
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  <PERSON>ll,
  <PERSON>,
  Brain,
  Baby,
  Calculator,
  Construction,
  Activity,
  Newspaper,
  Skull,
  StickyNote,
  MessageSquare,
  FileText,
  ClipboardList,
  FileText as FileDocument,
  AlertTriangle,
  GraduationCap,
  Droplets
} from "lucide-react";
import CategoryCard from "@/components/CategoryCard";
import { useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import AuthDialog from "@/components/auth/AuthDialog";
import { MedUnityBanner } from "@/components/promotions/MedUnityBanner";
import { DrWillCard } from "@/components/dr-will/DrWillCard";

const mainCategories = [
  {
    title: "Cálculo de Medicamentos",
    description: "Dosagens automáticas de forma fácil e rápida.",
    icon: Pill,
    color: "bg-gradient-to-br from-amber-100 via-amber-200 to-yellow-200 border-amber-300",
    path: "/medicamentos/painel",
    requiresAuth: false,
    badge: "Automático",
    isSpecial: true,
    specialGlow: true
  },
  {
    title: "Condutas e Manejos",
    description: "Protocolos e condutas para prática clínica.",
    icon: FileText,
    color: "bg-gradient-to-br from-emerald-100 via-emerald-200 to-green-200 border-emerald-300",
    path: "/condutas-e-manejos",
    requiresAuth: false,
    badge: "Novo",
    showNewBadge: false,
    isSpecial: true,
    specialGlow: true
  },
  {
    title: "Puericultura",
    description: "Crescimento, vacinas, DNPM e fórmulas infantis.",
    icon: Baby,
    color: "bg-gradient-to-br from-purple-100 via-purple-200 to-violet-200 border-purple-300",
    path: "/puericultura",
    requiresAuth: false,
    badge: "Completo e Automático",
    isSpecial: true,
    specialGlow: true
  },
  {
    title: "Bulas Profissionais",
    description: "Bulas completas de medicamentos para profissionais.",
    icon: FileDocument,
    color: "bg-gradient-to-br from-orange-100 via-orange-200 to-amber-200 border-orange-300",
    path: "/bulas-profissionais",
    requiresAuth: false,
    badge: "Completo",
    showNewBadge: false,
    showFeatureBadges: true,
    isSpecial: true,
    specialGlow: true
  },
  /* Temporariamente comentado para deploy
  {
    title: "Estudos de Pediatria",
    description: "Questões de pediatria para residência e concursos.",
    icon: GraduationCap,
    color: "bg-violet-200 border-gray-220",
    path: "/estudos",
    requiresAuth: false,
    badge: "Novo",
    showNewBadge: true,
    customBadgeText: "Beta",
    customBadgeColor: "purple"
  },
  */
  {
    title: "Assistente IA Dr. Will 2.0",
    description: "IA médica avançada com conhecimento em todas as especialidades.",
    icon: Brain,
    color: "bg-gradient-to-br from-indigo-100 via-indigo-200 to-blue-200 border-indigo-300",
    path: "/dr-will",
    requiresAuth: true,
    badge: "Novo",
    showNewBadge: true,
    customBadgeText: "2.0",
    customBadgeColor: "green",
    isSpecial: true,
    specialGlow: true
  },
  /* TEMPORARIAMENTE COMENTADO - NOTÍCIAS DIÁRIAS
  {
    title: "Notícias Diárias",
    description: "Atualizações e novidades do mundo da medicina pediátrica.",
    icon: Newspaper,
    color: "bg-blue-200 border-gray-220",
    path: "/newsletters",
    requiresAuth: false,
    badge: "Novo",
    showNewBadge: true
  },
  */
  {
    title: "Medicamentos na Amamentação",
    description: "Segurança de medicamentos durante a amamentação.",
    icon: Baby,
    color: "bg-gradient-to-br from-pink-100 via-pink-200 to-rose-200 border-pink-300",
    path: "/medicamentos-amamentacao",
    requiresAuth: false,
    badge: "Novo",
    showNewBadge: true,
    isSpecial: true,
    specialGlow: true
  },
  {
    title: "Interações medicamentosas",
    description: "Verifique interações entre medicamentos e evite riscos.",
    icon: AlertTriangle,
    color: "bg-gradient-to-br from-amber-100 via-amber-200 to-yellow-200 border-amber-300",
    path: "/interacoes-medicamentosas",
    requiresAuth: false,
    badge: "Automático",
    showNewBadge: false,
    showFeatureBadges: true,
    isSpecial: true,
    specialGlow: true
  },
  {
    title: "PediDrop",
    description: "Atualizações clínicas diárias em 5 minutos. Conteúdo direto ao ponto.",
    icon: Droplets,
    color: "bg-gradient-to-br from-blue-100 via-purple-100 to-indigo-100 border-blue-300",
    path: "/pedidrop",
    requiresAuth: false,
    badge: "7h da manhã",
    isSpecial: true,
    specialGlow: true
  },
  {
    title: "Prescrições",
    description: "Crie e gerencie prescrições personalizadas.",
    icon: Book,
    color: "bg-gradient-to-br from-red-100 via-red-200 to-rose-200 border-red-300",
    path: "/prescriptions",
    requiresAuth: true,
    badge: "Crie suas prescrições",
    isSpecial: true,
    specialGlow: true
  },
  {
    title: "Calculadoras e Escalas",
    description: "Calculadoras pediátricas de escores para uso diário.",
    icon: Calculator,
    color: "bg-gradient-to-br from-blue-100 via-blue-200 to-sky-200 border-blue-300",
    path: "/calculadoras",
    requiresAuth: false,
    badge: "Automático",
    isSpecial: true,
    specialGlow: true
  },
  {
    title: "Fluxogramas",
    description: "Manejo de urgências e emergências pediátricas.",
    icon: Activity,
    color: "bg-gradient-to-br from-cyan-100 via-cyan-200 to-teal-200 border-cyan-300",
    path: "/flowcharts",
    requiresAuth: false,
    badge: "Automático",
    isSpecial: true,
    specialGlow: true
  },
  {
    title: "Intoxicações",
    description: "Toxíndromes, antídotos e doses calculadas.",
    icon: Skull,
    color: "bg-gradient-to-br from-rose-100 via-rose-200 to-pink-200 border-rose-300",
    path: "/poisonings",
    requiresAuth: false,
    isSpecial: true,
    specialGlow: true
  },
  {
    title: "Anotações",
    description: "Organize suas anotações de forma prática e rápida, com acesso fácil de qualquer lugar.",
    icon: StickyNote,
    color: "bg-gradient-to-br from-yellow-100 via-yellow-200 to-amber-200 border-yellow-300",
    path: "/notes",
    requiresAuth: true,
    isSpecial: true,
    specialGlow: true
  },
  {
    title: "CID-10",
    description: "Consulta rápida de códigos CID.",
    icon: Book,
    color: "bg-gradient-to-br from-green-100 via-green-200 to-emerald-200 border-green-300",
    path: "/icd",
    requiresAuth: false,
    isSpecial: true,
    specialGlow: true
  },
  {
    title: "Bot WhatsApp",
    description: "Acesse as dosagens pediátricas 24 horas por dia através do nosso bot.",
    icon: MessageSquare,
    color: "bg-gradient-to-br from-green-100 via-green-200 to-emerald-200 border-green-300",
    path: "/whatsapp-bot",
    requiresAuth: false,
    badge: "24/7",
    isSpecial: true,
    specialGlow: true
  }
];

export const CategoryList: React.FC = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [showAuthDialog, setShowAuthDialog] = useState(false);
  const [pendingPath, setPendingPath] = useState<string | null>(null);

  const handleCategoryClick = async (path: string, requiresAuth: boolean) => {
    if (requiresAuth) {
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        setPendingPath(path);
        setShowAuthDialog(true);
        return;
      }
    }

    navigate(path);
  };

  return (
    <>
      {/* Grid com espaçamento ajustado para aparência de app */}
      <div className="grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 sm:gap-4 max-w-6xl mx-auto">
        {mainCategories.map((category, index) => {
          // Verificar se é o card do Dr. Will
          if (category.title.includes("Dr. Will")) {
            return (
              <div
                key={index}
                className="transform transition-all duration-300 relative"
                style={{
                  animationDelay: `${index * 50}ms`,
                  animation: 'fade-in-up 0.4s ease-out forwards',
                  opacity: 0
                }}
              >
                <DrWillCard
                  onClick={() => handleCategoryClick(category.path, true)}
                />
              </div>
            );
          }



          // Para todos os outros cards
          return (
            <div
              key={index}
              className="transform transition-all duration-300 relative"
              style={{
                animationDelay: `${index * 50}ms`,
                animation: 'fade-in-up 0.4s ease-out forwards',
                opacity: 0
              }}
            >
              <CategoryCard
                {...category}
                onClick={() => handleCategoryClick(category.path, category.requiresAuth || false)}
              />
            </div>
          );
        })}

        {/* MedUnity Banner Card */}
        <div
          className="transform transition-all duration-300 relative"
          style={{
            animationDelay: `${mainCategories.length * 50}ms`,
            animation: 'fade-in-up 0.4s ease-out forwards',
            opacity: 0
          }}
        >
          <MedUnityBanner />
        </div>
      </div>

      {showAuthDialog && (
        <AuthDialog
          defaultOpen={true}
          hidden={true}
          onOpenChange={(open) => {
            setShowAuthDialog(open);
            if (!open) {
              setPendingPath(null);
            }
          }}
        />
      )}
    </>
  );
};

