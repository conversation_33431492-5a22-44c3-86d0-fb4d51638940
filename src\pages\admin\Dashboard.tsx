import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  LucideLayoutDashboard,
  Users,
  Pill,
  Newspaper,
  Database,
  Activity,
  Bookmark,
  Settings,
  FileText,
  Beaker,
  Calculator,
  ListChecks,
  BookOpen,
  Tag,
  ChevronRight,
  Syringe,
  BarChart3,
  FileQuestion,
  BrainCircuit,
  Stethoscope,
  Baby,
  Shield,
  Milk,
  Wrench,
  Droplets
} from "lucide-react";
import { Link } from "react-router-dom";
import { useAuth } from "@/hooks/useAuth";

const AdminDashboard = () => {
  const { profile } = useAuth();
  const superAdminEmails = ['<EMAIL>', '<EMAIL>'];
  const isSuperAdmin = profile?.is_admin && superAdminEmails.includes(profile?.email || '');



  const modules = [
    {
      title: "Administradores",
      description: "Gerenciar usuários administrativos e permissões",
      icon: <Shield className="h-8 w-8 text-red-500" />,
      link: "/admin/admin-users"
    },
    {
      title: "Modo de Manutenção",
      description: "Controlar acesso ao site durante manutenções",
      icon: <Wrench className="h-8 w-8 text-orange-600" />,
      link: "/admin/maintenance",
      superAdminOnly: true
    },
    {
      title: "Blog",
      description: "Gerenciar posts, categorias e tags do blog",
      icon: <Newspaper className="h-8 w-8 text-blue-500" />,
      link: "/admin/blog"
    },
    {
      title: "PediDrop",
      description: "Gerenciar atualizações clínicas diárias em 5 minutos",
      icon: <Droplets className="h-8 w-8 text-blue-600" />,
      link: "/admin/pedidrop"
    },
    {
      title: "Categorias de Medicamentos",
      description: "Gerenciar categorias de medicamentos",
      icon: <Bookmark className="h-8 w-8 text-purple-500" />,
      link: "/admin/categories"
    },
    {
      title: "Medicamentos",
      description: "Gerenciar medicamentos e posologias",
      icon: <Pill className="h-8 w-8 text-green-500" />,
      link: "/admin/medications"
    },
    {
      title: "Fórmulas",
      description: "Gerenciar fórmulas médicas",
      icon: <Beaker className="h-8 w-8 text-amber-500" />,
      link: "/admin/formulas"
    },
    {
      title: "Dosagens",
      description: "Gerenciar dosagens de medicamentos",
      icon: <Calculator className="h-8 w-8 text-emerald-500" />,
      link: "/admin/dosages"
    },
    {
      title: "Bulas",
      description: "Gerenciar instruções de medicamentos",
      icon: <FileText className="h-8 w-8 text-yellow-500" />,
      link: "/admin/instructions"
    },
    {
      title: "Categorias de Prescrição",
      description: "Gerenciar categorias de prescrição",
      icon: <Tag className="h-8 w-8 text-orange-500" />,
      link: "/admin/prescription-categories"
    },
    {
      title: "CID-10",
      description: "Gerenciar códigos CID-10",
      icon: <Database className="h-8 w-8 text-indigo-500" />,
      link: "/admin/icd10"
    },
    {
      title: "Curvas de Crescimento",
      description: "Gerenciar curvas de crescimento",
      icon: <Activity className="h-8 w-8 text-red-500" />,
      link: "/admin/growth-curves"
    },
    {
      title: "Dados das Curvas",
      description: "Gerenciar metadados das curvas de crescimento",
      icon: <BarChart3 className="h-8 w-8 text-cyan-500" />,
      link: "/admin/growth-curve-metadata"
    },
    {
      title: "Importar Questões",
      description: "Importar questões para o sistema",
      icon: <FileQuestion className="h-8 w-8 text-violet-500" />,
      link: "/admin/question-import"
    },
    {
      title: "Formatação de Questões",
      description: "Formatar questões no sistema",
      icon: <BookOpen className="h-8 w-8 text-fuchsia-500" />,
      link: "/admin/question-formatting"
    },
    {
      title: "Formatar Temas",
      description: "Gerenciar formatação de temas",
      icon: <BrainCircuit className="h-8 w-8 text-pink-500" />,
      link: "/admin/format-themes"
    },
    {
      title: "Condutas e Manejos",
      description: "Gerenciar condutas e manejos clínicos",
      icon: <Stethoscope className="h-8 w-8 text-teal-500" />,
      link: "/admin/condutas-e-manejos"
    },
    {
      title: "Vacinas",
      description: "Gerenciar informações sobre vacinas",
      icon: <Syringe className="h-8 w-8 text-lime-500" />,
      link: "/admin/vaccines"
    },
    {
      title: "Medicamentos na Amamentação",
      description: "Aprimorar informações sobre medicamentos durante a amamentação",
      icon: <Milk className="h-8 w-8 text-pink-400" />,
      link: "/admin/breastfeeding-medications-enhancement"
    },
    {
      title: "Analytics de Medicamentos",
      description: "Análise detalhada do uso da plataforma de medicamentos",
      icon: <BarChart3 className="h-8 w-8 text-blue-600" />,
      link: "/admin/medication-analytics"
    },
    {
      title: "Marcos DNPM",
      description: "Gerenciar marcos de desenvolvimento neuropsicomotor",
      icon: <Baby className="h-8 w-8 text-rose-500" />,
      link: "/admin/dnpm"
    },
    {
      title: "Configurações",
      description: "Configurações do sistema",
      icon: <Settings className="h-8 w-8 text-gray-500" />,
      link: "/admin/settings"
    },
    {
      title: "Configurações do Site",
      description: "Configurações gerais do site",
      icon: <LucideLayoutDashboard className="h-8 w-8 text-slate-500" />,
      link: "/admin/site-settings"
    }
  ];

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Painel Administrativo</h1>
        <p className="text-gray-600 mb-4">
          Bem-vindo ao painel administrativo do Pediatria Descomplicada. Aqui você pode gerenciar todo o conteúdo e configurações do site.
        </p>
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-100">
          <CardContent className="pt-6">
            <div className="flex items-center gap-4">
              <div className="bg-blue-100 p-3 rounded-full">
                <LucideLayoutDashboard className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-blue-900">Gestão de Conteúdo Médico</h2>
                <p className="text-blue-700">
                  Este painel permite a manutenção de todas as informações médicas, incluindo medicamentos,
                  dosagens, bulas, CID-10 e outros recursos essenciais para profissionais de saúde.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* DEBUG: Mostrar informações do usuário */}
      <div className="mb-6 p-4 bg-yellow-50 border-2 border-yellow-200 rounded-lg">
        <h3 className="text-lg font-bold text-yellow-800 mb-3">🔍 DEBUG INFO - Sistema de Manutenção</h3>
        <div className="grid md:grid-cols-2 gap-4 text-sm">
          <div className="space-y-1">
            <p><strong>Email:</strong> {profile?.email || 'Não logado'}</p>
            <p><strong>User ID:</strong> {profile?.id || 'N/A'}</p>
            <p><strong>Full Name:</strong> {profile?.full_name || 'N/A'}</p>
            <p><strong>Is Admin:</strong> {profile?.is_admin ? '✅ Sim' : '❌ Não'}</p>
            <p><strong>Is Super Admin:</strong> {isSuperAdmin ? '✅ Sim' : '❌ Não'}</p>
          </div>
          <div className="space-y-1">
            <p><strong>Total Módulos:</strong> {modules.length}</p>
            <p><strong>Módulos Filtrados:</strong> {modules.filter(module => !module.superAdminOnly || isSuperAdmin).length}</p>
            <p><strong>Módulos Super Admin:</strong> {modules.filter(m => m.superAdminOnly).length}</p>
            <p><strong>Emails Super Admin:</strong> <EMAIL>, <EMAIL></p>
            <p><strong>Email Match:</strong> {superAdminEmails.includes(profile?.email || '') ? '✅ Sim' : '❌ Não'}</p>
          </div>
        </div>
        <div className="mt-3 p-2 bg-yellow-100 rounded">
          <p className="text-xs"><strong>Módulos Super Admin:</strong> {modules.filter(m => m.superAdminOnly).map(m => m.title).join(', ')}</p>
        </div>
        <div className="mt-2 p-2 bg-blue-100 rounded">
          <p className="text-xs"><strong>Condição Super Admin:</strong> is_admin={profile?.is_admin ? 'true' : 'false'} AND email='{profile?.email}' IN ['<EMAIL>', '<EMAIL>']</p>
        </div>
      </div>

      <h2 className="text-2xl font-semibold mb-4">Módulos Disponíveis</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {modules.filter(module => !module.superAdminOnly || isSuperAdmin).map((module, index) => (
          <Link to={module.link} key={index}>
            <Card className="h-full hover:shadow-md transition-shadow cursor-pointer border-l-4 border-l-primary">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg">{module.title}</CardTitle>
                  {module.icon}
                </div>
                <CardDescription>{module.description}</CardDescription>
              </CardHeader>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default AdminDashboard;
