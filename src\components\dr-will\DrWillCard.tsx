import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Brain, Sparkles, Image } from 'lucide-react';
import { useSession } from '@supabase/auth-helpers-react';
import AuthDialog from '@/components/auth/AuthDialog';
import { cn } from '@/lib/utils';

interface DrWillCardProps {
  onClick?: () => void;
}

export const DrWillCard: React.FC<DrWillCardProps> = ({ onClick }) => {
  const navigate = useNavigate();
  const session = useSession();
  const [showAuthDialog, setShowAuthDialog] = React.useState(false);

  const handleClick = () => {
    if (!session) {
      setShowAuthDialog(true);
      return;
    }

    if (onClick) {
      onClick();
      return;
    }

    navigate('/dr-will');
  };

  return (
    <>
      <div className="relative">
        {/* Version badge - "2.0" em estilo mais moderno */}
        <div className="absolute -top-2 -right-2 z-10">
          <div className="px-2 py-0.5 text-white text-xs font-medium rounded-md shadow-md flex items-center gap-1 bg-gradient-to-r from-indigo-500 to-blue-500 shadow-indigo-300/50">
            <Sparkles className="w-3 h-3" />
            2.0
          </div>
        </div>

        <div
          className={cn(
            "relative h-full p-4 sm:p-5 rounded-xl transition-all duration-300 cursor-pointer overflow-hidden group",
            "bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-md hover:shadow-lg hover:-translate-y-1",
            "border border-gray-100 dark:border-gray-700/50",
            "ring-2 ring-indigo-300/50 dark:ring-indigo-500/30",
            "shadow-indigo-200/50 dark:shadow-indigo-500/20 shadow-xl",
            "hover:shadow-indigo-300/60 dark:hover:shadow-indigo-400/30 hover:shadow-2xl"
          )}
          onClick={handleClick}
          style={{
            background: 'linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(59, 130, 246, 0.05) 50%, rgba(37, 99, 235, 0.1) 100%)',
            boxShadow: '0 0 30px rgba(37, 99, 235, 0.15), 0 10px 25px rgba(0, 0, 0, 0.1)'
          }}
        >
          {/* Barra de cor na parte superior para aparência de app */}
          <div className="absolute top-0 left-0 right-0 h-2 rounded-t-xl bg-gradient-to-r from-indigo-400 via-blue-400 to-indigo-500 shadow-lg shadow-indigo-200/50" />

          <div className="flex flex-col items-center text-center h-full relative z-10 justify-between pt-2">
            {/* Ícone com estrela no canto - Estilo mais moderno */}
            <div className="relative">
              <div className="w-14 h-14 sm:w-16 sm:h-16 rounded-lg flex items-center justify-center mb-3
                            bg-indigo-50 dark:bg-indigo-900/30 shadow-sm overflow-hidden relative">
                <Brain className="w-7 h-7 sm:w-8 sm:h-8 text-indigo-600 dark:text-indigo-400" />

                {/* Estrela no canto */}
                <div className="absolute -top-0.5 -right-0.5 text-yellow-400">
                  <Sparkles className="w-3 h-3" />
                </div>
              </div>
            </div>

            {/* Título com 2.0 em roxo */}
            <h3 className="font-bold text-gray-800 dark:text-gray-200 line-clamp-2 text-base sm:text-lg">
              Assistente IA Dr. Will <span className="text-indigo-600 dark:text-indigo-400">2.0</span>
            </h3>

            {/* Descrição - Igual aos outros cards */}
            <p className="hidden sm:block text-xs sm:text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mt-1.5 min-h-[32px]">
              IA médica avançada com conhecimento em todas as especialidades
            </p>

            {/* Feature badges - Estilo mais moderno */}
            <div className="mt-2 flex flex-wrap gap-1.5 justify-center">
              <div className="flex items-center gap-1 bg-gray-100/80 dark:bg-slate-700/80 text-indigo-700 dark:text-indigo-400
                            text-[10px] px-1.5 py-0.5 rounded-md">
                <Sparkles className="w-2.5 h-2.5" />
                <span className="hidden sm:inline">Especialidades</span>
              </div>
              <div className="flex items-center gap-1 bg-gray-100/80 dark:bg-slate-700/80 text-indigo-700 dark:text-indigo-400
                            text-[10px] px-1.5 py-0.5 rounded-md">
                <Image className="w-2.5 h-2.5" />
                <span className="hidden sm:inline">Análise de imagens</span>
              </div>
            </div>

            {/* "Atualizado" badge - Estilo mais moderno */}
            <div className="mt-2 flex gap-1.5 flex-wrap justify-center">
              <div className="inline-flex items-center rounded-md bg-gray-100/80 dark:bg-slate-700/80 border-none
                            px-2 py-0.5 text-[10px] text-gray-700 dark:text-gray-300 font-medium h-auto">
                Atualizado
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Auth dialog */}
      <AuthDialog
        open={showAuthDialog}
        onOpenChange={setShowAuthDialog}
        hidden={true}
      />
    </>
  );
};
