import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { motion } from 'framer-motion';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Calendar, 
  Clock, 
  Droplets,
  Search,
  Filter,
  ChevronLeft
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useNavigate } from 'react-router-dom';
import { usePediDrops, usePediDropsCount, useDeletePediDrop } from '@/hooks/usePediDrop';
import { PediDropEditor } from '@/components/admin/pedidrop/PediDropEditor';

const PediDropAdmin: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [showEditor, setShowEditor] = useState(false);
  const [editingDrop, setEditingDrop] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Buscar PediDrops
  const {
    data: drops,
    isLoading: isLoadingDrops,
    isError: isDropsError,
    refetch: refetchDrops
  } = usePediDrops({
    limit: itemsPerPage,
    offset: (currentPage - 1) * itemsPerPage,
    searchTerm: searchTerm || undefined,
    includeUnpublished: true // Admin pode ver todos
  });

  // Buscar total de drops
  const {
    data: totalCount,
    isLoading: isLoadingCount
  } = usePediDropsCount({
    searchTerm: searchTerm || undefined,
    includeUnpublished: true
  });

  // Hook para deletar
  const deleteMutation = useDeletePediDrop();

  const totalPages = Math.ceil((totalCount || 0) / itemsPerPage);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleCreateNew = () => {
    setEditingDrop(null);
    setShowEditor(true);
  };

  const handleEdit = (drop: any) => {
    setEditingDrop(drop);
    setShowEditor(true);
  };

  const handleCloseEditor = () => {
    setShowEditor(false);
    setEditingDrop(null);
    refetchDrops();
  };

  const handleDelete = async (dropId: string) => {
    if (window.confirm('Tem certeza que deseja excluir este PediDrop?')) {
      try {
        await deleteMutation.mutateAsync(dropId);
      } catch (error) {
        console.error('Erro ao deletar PediDrop:', error);
        alert('Erro ao deletar PediDrop. Tente novamente.');
      }
    }
  };

  if (showEditor) {
    return (
      <PediDropEditor
        drop={editingDrop}
        onClose={handleCloseEditor}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-slate-900">
      <Helmet>
        <title>Administração PediDrop | PedBook Admin</title>
      </Helmet>

      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => navigate('/admin')}
                className="flex items-center gap-2"
              >
                <ChevronLeft className="h-4 w-4" />
                Voltar ao Admin
              </Button>
              <div className="flex items-center gap-3">
                <Droplets className="h-8 w-8 text-blue-600" />
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                    Administração PediDrop
                  </h1>
                  <p className="text-gray-600 dark:text-gray-400">
                    Gerenciar atualizações clínicas diárias
                  </p>
                </div>
              </div>
            </div>
            <Button
              onClick={handleCreateNew}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Novo PediDrop
            </Button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg">
                    <Droplets className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Total PediDrops</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                      {isLoadingCount ? '...' : totalCount || 0}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="bg-green-100 dark:bg-green-900/30 p-2 rounded-lg">
                    <Calendar className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Este Mês</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                      {/* TODO: Calcular drops do mês */}
                      --
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="bg-yellow-100 dark:bg-yellow-900/30 p-2 rounded-lg">
                    <Clock className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Tempo Médio</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                      5 min
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="bg-purple-100 dark:bg-purple-900/30 p-2 rounded-lg">
                    <Eye className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Visualizações</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                      {/* TODO: Calcular visualizações */}
                      --
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filtros */}
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Buscar PediDrops..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Filtros
            </Button>
          </div>
        </motion.div>

        {/* Lista de PediDrops */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          {isLoadingDrops ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-3">
                        <Skeleton className="h-6 w-3/4" />
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-1/2" />
                        <div className="flex gap-2">
                          <Skeleton className="h-6 w-16" />
                          <Skeleton className="h-6 w-20" />
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Skeleton className="h-8 w-8" />
                        <Skeleton className="h-8 w-8" />
                        <Skeleton className="h-8 w-8" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : isDropsError ? (
            <Card>
              <CardContent className="p-8 text-center">
                <p className="text-red-600 dark:text-red-400">
                  Erro ao carregar PediDrops. Tente novamente.
                </p>
              </CardContent>
            </Card>
          ) : drops && drops.length > 0 ? (
            <div className="space-y-4">
              {drops.map((drop, index) => (
                <motion.div
                  key={drop.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-3 mb-3">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 line-clamp-1">
                              {drop.title}
                            </h3>
                            <Badge variant="outline" className="text-xs">
                              <Clock className="h-3 w-3 mr-1" />
                              {drop.reading_time || 5} min
                            </Badge>
                            {drop.category && (
                              <Badge variant="secondary" className="text-xs">
                                {drop.category}
                              </Badge>
                            )}
                            {!drop.is_published && (
                              <Badge variant="outline" className="text-xs text-orange-600 border-orange-300">
                                Rascunho
                              </Badge>
                            )}
                          </div>

                          <p className="text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
                            {drop.summary}
                          </p>

                          <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {formatDate(drop.pub_date)}
                            </div>
                            {drop.sections && (
                              <div>
                                {drop.sections.length} seções
                              </div>
                            )}
                            {drop.references && (
                              <div>
                                {drop.references.length} referências
                              </div>
                            )}
                            <div>
                              {drop.views_count} visualizações
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 ml-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(`/pedidrop`, '_blank')}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(drop)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(drop.id)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Droplets className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Nenhum PediDrop encontrado
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Comece criando seu primeiro PediDrop clicando no botão acima.
                </p>
                <Button onClick={handleCreateNew}>
                  <Plus className="h-4 w-4 mr-2" />
                  Criar Primeiro PediDrop
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Paginação */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-8">
              <Button
                variant="outline"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                Anterior
              </Button>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Página {currentPage} de {totalPages}
              </span>
              <Button
                variant="outline"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                Próximo
              </Button>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default PediDropAdmin;
