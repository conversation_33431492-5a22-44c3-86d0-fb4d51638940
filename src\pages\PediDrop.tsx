import React, { useState } from "react";
import { Helmet } from "react-helmet-async";
import { motion } from "framer-motion";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import {
  Droplets,
  Clock,
  ChevronLeft,
  Sparkles,
  Zap,
  Calendar,
  Bell,
  Share2,
  ChevronDown,
  RefreshCw
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { usePediDrops } from "@/hooks/usePediDrop";
import { PediDropCard } from "@/components/pedidrop/PediDropCard";
import { useNavigate } from "react-router-dom";

const PediDrop: React.FC = () => {
  const navigate = useNavigate();
  const [itemsToShow, setItemsToShow] = useState(5); // Começar com 5 drops
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Função para verificar se uma data é hoje
  const isToday = (dateString: string) => {
    const today = new Date();
    const checkDate = new Date(dateString);

    return (
      checkDate.getDate() === today.getDate() &&
      checkDate.getMonth() === today.getMonth() &&
      checkDate.getFullYear() === today.getFullYear()
    );
  };

  // Buscar drops mais recentes
  const {
    data: drops,
    isLoading: isLoadingDrops,
    isError: isDropsError,
    error: dropsError,
    refetch
  } = usePediDrops({
    limit: itemsToShow,
    offset: 0,
    includeUnpublished: false // Apenas publicados para usuários
  });

  // Log simplificado para verificar se o filtro está funcionando
  React.useEffect(() => {
    if (drops && drops.length > 0) {
      const now = new Date();

      console.log('📅 PediDrops carregados:', drops.length);
      console.log('📅 Data atual:', now.toLocaleString('pt-BR'));

      drops.forEach((drop, index) => {
        const dropDate = new Date(drop.pub_date);
        const isDropToday = isToday(drop.pub_date);

        console.log(`📅 ${index + 1}. ${drop.title}`);
        console.log(`   Data: ${dropDate.toLocaleString('pt-BR')}`);
        console.log(`   É hoje? ${isDropToday}`);
      });
    }
  }, [drops]);

  // Função para carregar mais drops
  const handleLoadMore = async () => {
    setIsLoadingMore(true);
    setItemsToShow(prev => prev + 5);
    // Simular delay para UX
    setTimeout(() => {
      setIsLoadingMore(false);
    }, 500);
  };

  // Função para atualizar drops
  const handleRefresh = () => {
    refetch();
  };

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-blue-50 via-purple-50/30 to-indigo-50/20 dark:from-slate-900 dark:via-slate-800/50 dark:to-slate-900">
      <Helmet>
        <title>PediDrop - Atualização Clínica em 5 Minutos | PedBook</title>
        <meta name="description" content="Atualizações clínicas diárias em pediatria. Conteúdo baseado em evidências, direto ao ponto, em apenas 5 minutos de leitura." />
        <meta name="keywords" content="pediatria, atualizações clínicas, medicina baseada em evidências, educação médica continuada" />
      </Helmet>

      <Header />

      <main className="flex-1 container mx-auto px-4 py-6">
        <div className="max-w-7xl mx-auto">
          {/* Header do PediDrop */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8 mt-2"
          >
            <div className="flex items-center justify-between mb-6">
              {/* Botão Voltar */}
              <Button
                variant="ghost"
                onClick={() => navigate('/')}
                className="flex items-center gap-2 h-9 px-3 hover:bg-blue-50/60 dark:hover:bg-slate-800/60 transition-all duration-300 hover:scale-105 rounded-lg"
                aria-label="Voltar ao menu inicial"
              >
                <ChevronLeft className="h-4 w-4" />
                <span className="hidden sm:inline text-sm font-medium">Voltar</span>
              </Button>

              {/* Botões de Ação */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  <span className="hidden sm:inline">Atualizar</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="hidden sm:flex items-center gap-2"
                >
                  <Bell className="h-4 w-4" />
                  Notificar
                </Button>
              </div>
            </div>

            {/* Hero Section */}
            <div className="text-center mb-8">
              <div className="flex items-center justify-center gap-3 mb-4">
                <div className="relative">
                  <Droplets className="h-12 w-12 text-blue-600 dark:text-blue-400" />
                  <Sparkles className="h-6 w-6 text-purple-500 absolute -top-1 -right-1 animate-pulse" />
                </div>
                <div>
                  <h1 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 dark:from-blue-400 dark:via-purple-400 dark:to-blue-600 text-transparent bg-clip-text">
                    PediDrop
                  </h1>
                  <p className="text-lg text-gray-600 dark:text-gray-300 font-medium">
                    Atualização clínica em 5 minutos
                  </p>
                </div>
              </div>

              <div className="max-w-3xl mx-auto mb-6">
                <p className="text-gray-700 dark:text-gray-300 text-lg leading-relaxed">
                  🩺 Todos os dias, às <strong>7h da manhã</strong>, publicamos conteúdo clínico novo, 
                  direto ao ponto, com base nas melhores evidências médicas — tudo em menos de 
                  <span className="inline-flex items-center gap-1 mx-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-700 dark:text-blue-300 font-semibold">
                    <Clock className="h-4 w-4" />
                    5 minutos
                  </span>
                  de leitura.
                </p>
              </div>

              {/* Features */}
              <div className="flex flex-wrap justify-center gap-3 mb-8">
                {[
                  { icon: Zap, text: "FDA & ANVISA", color: "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300" },
                  { icon: Calendar, text: "Protocolos", color: "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300" },
                  { icon: Sparkles, text: "Guidelines", color: "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300" },
                  { icon: Clock, text: "Aplicação Prática", color: "bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300" }
                ].map((feature, index) => (
                  <Badge key={index} variant="secondary" className={`${feature.color} px-3 py-1.5 text-sm font-medium`}>
                    <feature.icon className="h-4 w-4 mr-1.5" />
                    {feature.text}
                  </Badge>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Conteúdo Principal */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {isLoadingDrops ? (
              <div className="space-y-6">
                {Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Skeleton className="h-6 w-6 rounded" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                      <Skeleton className="h-6 w-3/4" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-2/3" />
                      <div className="flex gap-2">
                        <Skeleton className="h-6 w-16" />
                        <Skeleton className="h-6 w-20" />
                        <Skeleton className="h-6 w-18" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : isDropsError ? (
              <div className="text-center py-12">
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 max-w-md mx-auto">
                  <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
                    Erro ao carregar PediDrops
                  </h3>
                  <p className="text-red-600 dark:text-red-300 mb-4">
                    {dropsError?.message || 'Não foi possível carregar as atualizações clínicas.'}
                  </p>
                  <Button
                    variant="outline"
                    className="border-red-300 text-red-700 hover:bg-red-50 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/30"
                    onClick={handleRefresh}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Tentar Novamente
                  </Button>
                </div>
              </div>
            ) : drops && drops.length > 0 ? (
              <>
                {/* PediDrop em destaque */}
                {drops.length > 0 && (
                  <div className="mb-12">
                    <PediDropCard
                      key={drops[0].id}
                      drop={drops[0]}
                      index={0}
                      variant="featured"
                      isToday={isToday(drops[0].pub_date)}
                    />
                  </div>
                )}

                {/* PediDrops Anteriores - outros drops */}
                {drops.length > 1 && (
                  <div className="mb-8 max-w-4xl mx-auto">
                    {/* Header PediDrops Anteriores */}
                    <div className="bg-gray-800 dark:bg-gray-900 text-white text-center py-3 rounded-t-lg">
                      <h2 className="text-sm font-bold tracking-wider uppercase">
                        PEDIDROPS ANTERIORES
                      </h2>
                    </div>

                    {/* Container dos PediDrops Anteriores */}
                    <div className="bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700 rounded-b-lg shadow-lg p-6">
                      <div className="space-y-4">
                        {drops.slice(1).map((drop, index) => (
                          <PediDropCard
                            key={drop.id}
                            drop={drop}
                            index={index + 1}
                            variant="standard"
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Botão Carregar Mais */}
                {drops.length >= itemsToShow && (
                  <div className="text-center">
                    <Button
                      onClick={handleLoadMore}
                      disabled={isLoadingMore}
                      size="lg"
                      className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3"
                    >
                      {isLoadingMore ? (
                        <>
                          <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                          Carregando...
                        </>
                      ) : (
                        <>
                          <ChevronDown className="h-5 w-5 mr-2" />
                          Carregar Mais PediDrops
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-8 max-w-md mx-auto">
                  <Droplets className="h-16 w-16 text-blue-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">
                    Nenhum PediDrop encontrado
                  </h3>
                  <p className="text-blue-600 dark:text-blue-300">
                    Não há atualizações clínicas disponíveis no momento.
                    Volte amanhã às 7h para novos conteúdos!
                  </p>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default PediDrop;
