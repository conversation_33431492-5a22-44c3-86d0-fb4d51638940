import React, { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { ChevronLeft, Stethoscope, AlertTriangle, Clock, Syringe } from "lucide-react";
import { Link } from "react-router-dom";
import { CalculatorSEO } from "@/components/seo/CalculatorSEO";
import { getThemeClasses } from "@/components/ui/theme-utils";
import { useSiteAnalytics } from "@/hooks/useMedicationAnalytics";

interface IntubationValues {
  weight: number | null;
  age: number | null;
  ageUnit: 'months' | 'years';
  indication: string;
  selectedMedications: {
    premedication: string[];
    induction: string[];
    neuromuscular: string[];
  };
}

interface MedicationResult {
  name: string;
  dose: string;
  volume: string;
  presentation: string;
  route: string;
  timing: string;
  observations: string;
  category: 'premedication' | 'induction' | 'neuromuscular';
}

const IntubationCalculator = () => {
  const { trackCalculatorUse } = useSiteAnalytics();

  const [values, setValues] = useState<IntubationValues>({
    weight: null,
    age: null,
    ageUnit: 'months',
    indication: '',
    selectedMedications: {
      premedication: [],
      induction: [],
      neuromuscular: []
    }
  });

  const [results, setResults] = useState<MedicationResult[]>([]);

  const indications = [
    { value: 'emergency', label: 'Emergência Geral' },
    { value: 'tce', label: 'TCE/Hipertensão Intracraniana' },
    { value: 'shock', label: 'Choque/Instabilidade' },
    { value: 'asthma', label: 'Asma/Broncoespasmo' },
    { value: 'status', label: 'Status Epilepticus' },
    { value: 'stable', label: 'Paciente Estável' }
  ];

  const medications = {
    premedication: [
      {
        id: 'atropine',
        name: 'Atropina',
        indication: 'Prevenção de bradicardia (RN, lactentes, succinilcolina)',
        dose: (weight: number) => Math.max(0.1, Math.min(0.5, weight * 0.02)),
        presentation: '0,25 mg/mL ou 0,5 mg/mL',
        route: 'Bolus IV rápido',
        timing: '1-2 min / 30-60 min',
        observations: 'Recomendado <1 ano ou uso de succinilcolina. Administração lenta pode causar bradicardia paradoxal.'
      },
      {
        id: 'lidocaine',
        name: 'Lidocaína',
        indication: 'TCE ou HIC (reduz aumento de PIC na laringoscopia)',
        dose: (weight: number) => weight * 1.5,
        presentation: '1% ou 2% (10 ou 20 mg/mL)',
        route: 'Bolus IV lento (30-60s)',
        timing: '1 min / 10-20 min',
        observations: 'Usar só se clara indicação'
      }
    ],
    induction: [
      {
        id: 'ketamine',
        name: 'Cetamina',
        indication: 'Sedação + analgesia (ideal em choque ou asma)',
        dose: (weight: number) => weight * 2,
        presentation: '50 mg/mL',
        route: 'Bolus IV',
        timing: '1 min / 10-20 min',
        observations: 'Aumenta secreções (considerar atropina). Segura e eficaz em TCE/HIC, pois mantém a pressão de perfusão cerebral.'
      },
      {
        id: 'etomidate',
        name: 'Etomidato',
        indication: 'Sedação em instáveis ou TCE',
        dose: (weight: number) => weight * 0.3,
        presentation: '2 mg/mL',
        route: 'Bolus IV',
        timing: '30 s / 3-5 min',
        observations: 'Supressão adrenal transitória (risco mínimo em dose única). Não tem efeito analgésico.'
      },
      {
        id: 'propofol',
        name: 'Propofol',
        indication: 'Sedação em estáveis',
        dose: (weight: number) => weight * 2,
        presentation: '10 mg/mL',
        route: 'Bolus IV lento',
        timing: '30 s / 5-10 min',
        observations: 'Risco de hipotensão (evitar em instáveis)'
      },
      {
        id: 'midazolam',
        name: 'Midazolam',
        indication: 'Sedação + amnésia (ex: status)',
        dose: (weight: number) => weight * 0.3,
        presentation: '5 mg/mL',
        route: 'Infusão lenta (2-5 min)',
        timing: '2-3 min / 30-60 min',
        observations: 'Sem efeito analgésico'
      },
      {
        id: 'fentanyl',
        name: 'Fentanil',
        indication: 'Analgesia e controle autonômico (TCE, sepse)',
        dose: (weight: number) => weight * 3, // mcg
        presentation: '50 mcg/mL',
        route: 'IV lento (≥30s)',
        timing: '1-2 min / 30-60 min',
        observations: 'Risco de rigidez torácica se bolus rápido'
      }
    ],
    neuromuscular: [
      {
        id: 'succinylcholine',
        name: 'Succinilcolina',
        indication: 'Paralisia rápida e breve',
        dose: (weight: number, age: number, ageUnit: string) => {
          const ageInYears = ageUnit === 'months' ? age / 12 : age;
          return ageInYears < 2 ? weight * 2 : weight * 1.5;
        },
        presentation: '50 mg/mL ou pó 100 mg',
        route: 'Bolus IV',
        timing: '30-60 s / 4-10 min',
        observations: 'Contraindicações: hipertermia maligna, trauma >48h, doenças neuromusculares'
      },
      {
        id: 'rocuronium',
        name: 'Rocurônio',
        indication: 'Alternativa à succinilcolina (ação intermediária)',
        dose: (weight: number) => weight * 1.2,
        presentation: '10 mg/mL',
        route: 'Bolus IV',
        timing: '60 s / 30-60 min',
        observations: 'Pode ser revertido com sugamadex'
      },
      {
        id: 'vecuronium',
        name: 'Vecurônio',
        indication: 'Paralisia intermediária',
        dose: (weight: number) => weight * 0.1,
        presentation: 'pó 10 mg (1 mg/mL)',
        route: 'Bolus IV',
        timing: '2-3 min / 30-45 min',
        observations: 'Evitar em RN (ação prolongada)'
      },
      {
        id: 'atracurium',
        name: 'Atracúrio',
        indication: 'Alternativa em disfunção hepatorrenal',
        dose: (weight: number) => weight * 0.5,
        presentation: '10 mg/mL',
        route: 'IV lento',
        timing: '2-3 min / 20-35 min',
        observations: 'Libera histamina (risco de hipotensão)'
      },
      {
        id: 'cisatracurium',
        name: 'Cisatracúrio',
        indication: 'Similar ao atracúrio (sem histamina)',
        dose: (weight: number) => weight * 0.15,
        presentation: '2 mg/mL',
        route: 'IV lento',
        timing: '2,5 min / 40-60 min',
        observations: 'Ideal para UTI e disfunção orgânica múltipla'
      }
    ]
  };

  const calculateMedications = () => {
    if (!values.weight || !values.age) return;

    const newResults: MedicationResult[] = [];

    // Calcular pré-medicações selecionadas
    values.selectedMedications.premedication.forEach(medId => {
      const med = medications.premedication.find(m => m.id === medId);
      if (med) {
        const doseValue = med.dose(values.weight!);
        const volume = med.presentation.includes('0,25') ? doseValue / 0.25 : doseValue / 0.5;
        
        newResults.push({
          name: med.name,
          dose: `${doseValue.toFixed(2)} mg`,
          volume: `${volume.toFixed(2)} mL`,
          presentation: med.presentation,
          route: med.route,
          timing: med.timing,
          observations: med.observations,
          category: 'premedication'
        });
      }
    });

    // Calcular induções selecionadas
    values.selectedMedications.induction.forEach(medId => {
      const med = medications.induction.find(m => m.id === medId);
      if (med) {
        const doseValue = med.dose(values.weight!);
        let volume = 0;
        let doseUnit = 'mg';
        
        if (med.id === 'fentanyl') {
          doseUnit = 'mcg';
          volume = doseValue / 50; // 50 mcg/mL
        } else {
          const concentration = med.presentation.includes('50') ? 50 : 
                              med.presentation.includes('10') ? 10 : 
                              med.presentation.includes('5') ? 5 : 2;
          volume = doseValue / concentration;
        }
        
        newResults.push({
          name: med.name,
          dose: `${doseValue.toFixed(2)} ${doseUnit}`,
          volume: `${volume.toFixed(2)} mL`,
          presentation: med.presentation,
          route: med.route,
          timing: med.timing,
          observations: med.observations,
          category: 'induction'
        });
      }
    });

    // Calcular bloqueadores neuromusculares selecionados
    values.selectedMedications.neuromuscular.forEach(medId => {
      const med = medications.neuromuscular.find(m => m.id === medId);
      if (med) {
        const doseValue = med.id === 'succinylcholine' 
          ? med.dose(values.weight!, values.age!, values.ageUnit)
          : med.dose(values.weight!);
        
        const concentration = med.presentation.includes('50') ? 50 : 
                            med.presentation.includes('10') ? 10 : 
                            med.presentation.includes('2') ? 2 : 1;
        const volume = doseValue / concentration;
        
        newResults.push({
          name: med.name,
          dose: `${doseValue.toFixed(2)} mg`,
          volume: `${volume.toFixed(2)} mL`,
          presentation: med.presentation,
          route: med.route,
          timing: med.timing,
          observations: med.observations,
          category: 'neuromuscular'
        });
      }
    });

    setResults(newResults);
  };

  useEffect(() => {
    calculateMedications();
  }, [values.weight, values.age, values.ageUnit, values.selectedMedications]);

  const handleMedicationToggle = (category: keyof IntubationValues['selectedMedications'], medId: string) => {
    setValues(prev => ({
      ...prev,
      selectedMedications: {
        ...prev.selectedMedications,
        [category]: prev.selectedMedications[category].includes(medId)
          ? prev.selectedMedications[category].filter(id => id !== medId)
          : [...prev.selectedMedications[category], medId]
      }
    }));
  };

  const getRecommendedMedications = () => {
    const recommendations = {
      premedication: [] as string[],
      induction: [] as string[],
      neuromuscular: [] as string[]
    };

    // Recomendações baseadas na indicação
    switch (values.indication) {
      case 'tce':
        recommendations.premedication.push('lidocaine');
        recommendations.induction.push('etomidate', 'fentanyl');
        recommendations.neuromuscular.push('rocuronium');
        break;
      case 'shock':
        recommendations.induction.push('ketamine', 'etomidate');
        recommendations.neuromuscular.push('rocuronium');
        break;
      case 'asthma':
        recommendations.induction.push('ketamine');
        recommendations.neuromuscular.push('rocuronium');
        break;
      case 'status':
        recommendations.induction.push('midazolam');
        recommendations.neuromuscular.push('rocuronium');
        break;
      case 'stable':
        recommendations.induction.push('propofol', 'fentanyl');
        recommendations.neuromuscular.push('rocuronium');
        break;
      default:
        recommendations.induction.push('ketamine');
        recommendations.neuromuscular.push('rocuronium');
    }

    // Recomendações baseadas na idade
    if (values.age && values.ageUnit === 'months' && values.age < 12) {
      recommendations.premedication.push('atropine');
    }

    return recommendations;
  };

  const recommendedMeds = getRecommendedMedications();

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col")}>
      <CalculatorSEO 
        title="Calculadora de Medicações para Intubação Orotraqueal Pediátrica"
        description="Calcule automaticamente as doses de medicações para intubação orotraqueal em pediatria. Inclui pré-medicação, indução e bloqueadores neuromusculares."
        keywords="intubação, pediatria, medicações, doses, emergência, UTI pediátrica"
        category="Emergência"
      />
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Link to="/calculadoras">
              <Button variant="ghost" size="icon" className="hover:bg-primary/10 dark:hover:bg-primary/20">
                <ChevronLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h1 className={getThemeClasses.gradientHeading("text-3xl")}>
              Medicações para Intubação Orotraqueal Pediátrica
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-300">
            Calculadora automática para doses de medicações utilizadas na intubação orotraqueal pediátrica
          </p>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Dados do Paciente */}
            <Card className={getThemeClasses.card("p-6 space-y-6")}>
              <h2 className="text-xl font-semibold flex items-center gap-2">
                <Stethoscope className="h-5 w-5 text-primary" />
                Dados do Paciente
              </h2>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Peso (kg)</Label>
                  <Input
                    type="number"
                    placeholder="Ex: 15"
                    value={values.weight || ''}
                    onChange={(e) => setValues(prev => ({ ...prev, weight: parseFloat(e.target.value) || null }))}
                    className={getThemeClasses.input()}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Idade</Label>
                    <Input
                      type="number"
                      placeholder="Ex: 24"
                      value={values.age || ''}
                      onChange={(e) => setValues(prev => ({ ...prev, age: parseFloat(e.target.value) || null }))}
                      className={getThemeClasses.input()}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Unidade</Label>
                    <Select
                      value={values.ageUnit}
                      onValueChange={(value: 'months' | 'years') => setValues(prev => ({ ...prev, ageUnit: value }))}
                    >
                      <SelectTrigger className={getThemeClasses.select()}>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="months">Meses</SelectItem>
                        <SelectItem value="years">Anos</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Indicação Clínica</Label>
                  <Select
                    value={values.indication}
                    onValueChange={(value) => setValues(prev => ({ ...prev, indication: value }))}
                  >
                    <SelectTrigger className={getThemeClasses.select()}>
                      <SelectValue placeholder="Selecione a indicação" />
                    </SelectTrigger>
                    <SelectContent>
                      {indications.map((indication) => (
                        <SelectItem key={indication.value} value={indication.value}>
                          {indication.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </Card>

            {/* Seleção de Medicações */}
            <Card className={getThemeClasses.card("p-6 space-y-6")}>
              <h2 className="text-xl font-semibold flex items-center gap-2">
                <Syringe className="h-5 w-5 text-primary" />
                Seleção de Medicações
              </h2>

              {/* Pré-medicação */}
              <div className="space-y-3">
                <h3 className="font-medium text-amber-600 dark:text-amber-400">📍 Pré-Medicação</h3>
                <div className="space-y-2">
                  {medications.premedication.map((med) => (
                    <div key={med.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{med.name}</span>
                          {recommendedMeds.premedication.includes(med.id) && (
                            <Badge variant="secondary" className="text-xs">Recomendado</Badge>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">{med.indication}</p>
                      </div>
                      <Button
                        variant={values.selectedMedications.premedication.includes(med.id) ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleMedicationToggle('premedication', med.id)}
                      >
                        {values.selectedMedications.premedication.includes(med.id) ? 'Selecionado' : 'Selecionar'}
                      </Button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Indução */}
              <div className="space-y-3">
                <h3 className="font-medium text-blue-600 dark:text-blue-400">⚡ Indução (Sedoanalgesia)</h3>
                <div className="space-y-2">
                  {medications.induction.map((med) => (
                    <div key={med.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{med.name}</span>
                          {recommendedMeds.induction.includes(med.id) && (
                            <Badge variant="secondary" className="text-xs">Recomendado</Badge>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">{med.indication}</p>
                      </div>
                      <Button
                        variant={values.selectedMedications.induction.includes(med.id) ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleMedicationToggle('induction', med.id)}
                      >
                        {values.selectedMedications.induction.includes(med.id) ? 'Selecionado' : 'Selecionar'}
                      </Button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Bloqueadores Neuromusculares */}
              <div className="space-y-3">
                <h3 className="font-medium text-red-600 dark:text-red-400">🔒 Bloqueadores Neuromusculares</h3>
                <div className="space-y-2">
                  {medications.neuromuscular.map((med) => (
                    <div key={med.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{med.name}</span>
                          {recommendedMeds.neuromuscular.includes(med.id) && (
                            <Badge variant="secondary" className="text-xs">Recomendado</Badge>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">{med.indication}</p>
                      </div>
                      <Button
                        variant={values.selectedMedications.neuromuscular.includes(med.id) ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleMedicationToggle('neuromuscular', med.id)}
                      >
                        {values.selectedMedications.neuromuscular.includes(med.id) ? 'Selecionado' : 'Selecionar'}
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          </div>

          {/* Resultados */}
          {results.length > 0 && values.weight && (
            <Card className={getThemeClasses.card("p-6")}>
              <h2 className="text-xl font-semibold mb-6 flex items-center gap-2">
                <Clock className="h-5 w-5 text-primary" />
                Doses Calculadas - Peso: {values.weight} kg
              </h2>

              <div className="space-y-6">
                {['premedication', 'induction', 'neuromuscular'].map((category) => {
                  const categoryResults = results.filter(r => r.category === category);
                  if (categoryResults.length === 0) return null;

                  const categoryNames = {
                    premedication: '📍 Pré-Medicação',
                    induction: '⚡ Indução (Sedoanalgesia)',
                    neuromuscular: '🔒 Bloqueadores Neuromusculares'
                  };

                  return (
                    <div key={category} className="space-y-4">
                      <h3 className="font-medium text-lg">{categoryNames[category as keyof typeof categoryNames]}</h3>
                      <div className="grid gap-4">
                        {categoryResults.map((result, index) => (
                          <div key={index} className="border rounded-lg p-4 space-y-3">
                            <div className="flex items-center justify-between">
                              <h4 className="font-semibold text-lg">{result.name}</h4>
                              <div className="text-right">
                                <div className="text-2xl font-bold text-primary">{result.dose}</div>
                                <div className="text-sm text-gray-500">({result.volume})</div>
                              </div>
                            </div>
                            
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                              <div>
                                <span className="font-medium">Apresentação:</span>
                                <p className="text-gray-600 dark:text-gray-400">{result.presentation}</p>
                              </div>
                              <div>
                                <span className="font-medium">Via/Modo:</span>
                                <p className="text-gray-600 dark:text-gray-400">{result.route}</p>
                              </div>
                              <div>
                                <span className="font-medium">Início/Duração:</span>
                                <p className="text-gray-600 dark:text-gray-400">{result.timing}</p>
                              </div>
                            </div>
                            
                            {result.observations && (
                              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded p-3">
                                <div className="flex items-start gap-2">
                                  <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
                                  <p className="text-sm text-yellow-800 dark:text-yellow-200">{result.observations}</p>
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>

              <div className="mt-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-red-800 dark:text-red-200">
                    <p className="font-medium mb-2">⚠️ AVISOS IMPORTANTES:</p>
                    <ul className="space-y-1 list-disc list-inside">
                      <li>Todos os bloqueadores neuromusculares requerem sedação prévia completa</li>
                      <li>Monitorização contínua obrigatória durante todo o procedimento</li>
                      <li>Tenha sempre disponível equipamentos de ventilação e reversão</li>
                      <li>Esta calculadora é uma ferramenta auxiliar - sempre considere o contexto clínico</li>
                    </ul>
                  </div>
                </div>
              </div>
            </Card>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default IntubationCalculator;
