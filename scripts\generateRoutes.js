/**
 * Script para gerar rotas dinamicamente para prerendering
 * Busca medicamentos do Supabase e cria lista de rotas para SEO
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Configuração do Supabase
const supabaseUrl = 'https://bxedpdmgvgatjdfxgxij.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ4ZWRwZG1ndmdhdGpkZnhneGlqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzIyNzk3MTgsImV4cCI6MjA0Nzg1NTcxOH0.cjoaggOXt1kY9WmVNbAipCOQ2dP4PWLP43KMf8cO8Wo';

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Busca todos os medicamentos do Supabase
 */
async function fetchMedications() {
  console.log('🔍 Buscando medicamentos do Supabase...');

  try {
    console.log('🔍 Tentando conectar com Supabase...');

    const { data: medications, error } = await supabase
      .from('pedbook_medications')
      .select('id, slug, name, brands, contraindications, guidelines, scientific_references');

    if (error) {
      console.error('❌ Erro ao buscar medicamentos:', error);
      console.log('🔄 Usando dados de fallback...');
      return getFallbackMedications();
    }

    if (medications && medications.length > 0) {
      console.log(`✅ Encontrados ${medications.length} medicamentos do Supabase`);
      return medications;
    } else {
      console.log('⚠️ Nenhum medicamento encontrado, usando fallback...');
      return getFallbackMedications();
    }

  } catch (error) {
    console.error('❌ Erro na conexão com Supabase:', error);
    console.log('🔄 Usando dados de fallback...');
    return getFallbackMedications();
  }
}

/**
 * Dados de fallback quando a API falha
 */
function getFallbackMedications() {
  return [
    {
      id: 'a5555724-155b-44fb-a036-64c5a964c2ef',
      slug: 'paracetamol',
      name: 'Paracetamol',
      description: '',
      brands: 'Tylenol®, Tylenol AP®, Tylenol Bebê®, Tylenol Criança®',
      contraindications: '- Hipersensibilidade ao paracetamol ou a qualquer componente da fórmula.\n- Deficiência de G6PD;\n- Doença hepática grave;',
      guidelines: 'Em caso de intoxicação:\n- Medidas de Suporte: Iniciar a descontaminação gastrointestinal utilizando carvão ativado, especialmente se a ingestão ocorreu dentro das últimas 4 horas.\n- Antídoto: Avaliar a necessidade de iniciar o tratamento com N-Acetilcisteína (NAC). Consulte a seção "intoxicação por acetaminofeno" no aplicativo para orientações detalhadas sobre dosagem e administração do antídoto.\n- Monitoramento: Realizar dosagem sérica de paracetamol para avaliar o risco de toxicidade hepática e guiar o tratamento.\n- Suporte Adicional: Fornecer cuidados de suporte apropriados, incluindo hidratação adequada e monitoramento contínuo das funções hepática e renal.',
      scientific_references: 'Wolters Kluwer. Acetaminophen (paracetamol): Pediatric drug information. UpToDate. Disponível em: https://www.uptodate.com/contents/acetaminophen-paracetamol-pediatric-drug-information. Acesso em: 22 fev. 2025.\n\nGeolab Indústria Farmacêutica. Paracetamol: bula para profissionais de saúde. Anápolis: Geolab, 2023. Disponível em: https://www.geolab.com.br/wp-content/uploads/2023/05/PARACETAMOL-Bula-Profissional.pdf. Acesso em: 22 fev. 2025.',
      pedbook_medication_categories: { name: 'Analgésicos / Antitérmicos' },
      pedbook_medication_dosages: [
        {
          name: 'Suspensão oral 32 mg/mL',
          summary: '- Dose: 10 a 15 mg/kg/dose;\n- Dose máxima: 75mg/kg ou 1000mg/dose e 4000mg/dia;',
          dosage_template: 'Tomar de ((suspensao1)) a ((suspensaofinal)) mL de 6 em 6 horas'
        },
        {
          name: 'Solução oral 200 mg/mL - Gotas',
          summary: '- Dose: 1 gota/kg;\n- Dose máxima para crianças < 12 anos: 35 gotas/dose;\n- Dose máxima para crianças > 12 anos: 55 gotas/dose;',
          dosage_template: 'Tomar ((gotas)) gotas de 6 em 6 horas'
        }
      ]
    },
    {
      slug: 'amoxicilina',
      name: 'Amoxicilina',
      description: 'Antibiótico de amplo espectro para pediatria',
      brands: 'Amoxil®, Novamox®, Hiconcil®',
      contraindications: '- Hipersensibilidade à amoxicilina ou penicilinas',
      guidelines: 'Administrar preferencialmente com alimentos para reduzir efeitos gastrointestinais',
      pedbook_medication_categories: { name: 'Antimicrobianos (Antibióticos)' },
      pedbook_medication_dosages: [
        {
          name: 'Suspensão oral 250mg/5mL',
          summary: '- Dose: 25-50mg/kg/dia dividido em 2-3 doses',
          dosage_template: 'Tomar ((dose)) mL de 8 em 8 horas'
        }
      ]
    },
    {
      slug: 'dipirona',
      name: 'Dipirona',
      description: 'Analgésico e antipirético pediátrico',
      brands: 'Novalgina®, Anador®, Dorflex®',
      contraindications: '- Hipersensibilidade à dipirona\n- Deficiência de G6PD',
      guidelines: 'Monitorar sinais de agranulocitose em uso prolongado',
      pedbook_medication_categories: { name: 'Analgésicos / Antitérmicos' },
      pedbook_medication_dosages: [
        {
          name: 'Solução oral 500mg/mL - Gotas',
          summary: '- Dose: 10-15mg/kg/dose',
          dosage_template: 'Tomar ((gotas)) gotas de 6 em 6 horas'
        }
      ]
    }
  ];
}

/**
 * Busca todas as bulas profissionais do Supabase
 */
async function fetchProfessionalLabels() {
  console.log('📋 Buscando bulas profissionais do Supabase...');

  try {
    const { data: labels, error } = await supabase
      .from('pedbook_medication_instructions')
      .select('id, slug, title, content');

    if (error) {
      console.error('❌ Erro ao buscar bulas:', error);
      return [];
    }

    if (labels && labels.length > 0) {
      console.log(`✅ Encontradas ${labels.length} bulas profissionais do Supabase`);
      return labels;
    } else {
      console.log('⚠️ Nenhuma bula encontrada');
      return [];
    }

  } catch (error) {
    console.error('❌ Erro na conexão com Supabase para bulas:', error);
    return [];
  }
}

/**
 * Busca todas as condutas e manejos do Supabase
 */
async function fetchConducts() {
  console.log('📚 Buscando condutas e manejos do Supabase...');

  try {
    const { data: conducts, error } = await supabase
      .from('pedbook_conducts')
      .select('id, slug, title, content');

    if (error) {
      console.error('❌ Erro ao buscar condutas:', error);
      return [];
    }

    if (conducts && conducts.length > 0) {
      console.log(`✅ Encontradas ${conducts.length} condutas e manejos do Supabase`);
      return conducts;
    } else {
      console.log('⚠️ Nenhuma conduta encontrada');
      return [];
    }

  } catch (error) {
    console.error('❌ Erro na conexão com Supabase para condutas:', error);
    return [];
  }
}

/**
 * Busca medicamentos na amamentação do Supabase
 */
async function fetchBreastfeedingMedications() {
  console.log('🤱 Buscando medicamentos na amamentação do Supabase...');

  try {
    const { data: medications, error } = await supabase.rpc('get_breastfeeding_structure_cached');

    if (error) {
      console.error('❌ Erro ao buscar medicamentos na amamentação:', error);
      return [];
    }

    if (medications && medications.sections) {
      console.log(`✅ Encontradas ${medications.sections.length} seções de medicamentos na amamentação`);

      // Extrair medicamentos individuais
      const allMedications = [];
      medications.sections.forEach(section => {
        if (section.medications) {
          section.medications.forEach(med => {
            allMedications.push({
              id: med.id,
              name: med.name,
              slug: med.name.toLowerCase()
                .replace(/[^a-z0-9\s]/g, '')
                .replace(/\s+/g, '-')
                .trim(),
              compatibility_level: med.compatibility_level,
              usage_description: med.usage_description,
              section_name: section.name
            });
          });
        }

        if (section.subsections) {
          section.subsections.forEach(subsection => {
            if (subsection.medications) {
              subsection.medications.forEach(med => {
                allMedications.push({
                  id: med.id,
                  name: med.name,
                  slug: med.name.toLowerCase()
                    .replace(/[^a-z0-9\s]/g, '')
                    .replace(/\s+/g, '-')
                    .trim(),
                  compatibility_level: med.compatibility_level,
                  usage_description: med.usage_description,
                  section_name: section.name,
                  subsection_name: subsection.name
                });
              });
            }
          });
        }
      });

      console.log(`✅ Total de ${allMedications.length} medicamentos na amamentação encontrados`);
      return allMedications;
    } else {
      console.log('⚠️ Nenhum medicamento na amamentação encontrado');
      return [];
    }

  } catch (error) {
    console.error('❌ Erro na conexão com Supabase para medicamentos na amamentação:', error);
    return [];
  }
}

/**
 * Busca todas as calculadoras disponíveis
 */
async function fetchCalculators() {
  console.log('🧮 Buscando calculadoras...');

  // Lista estática de calculadoras (pode ser expandida)
  const calculators = [
    { slug: 'imc', name: 'Calculadora de IMC Pediátrico' },
    { slug: 'hidratacao', name: 'Calculadora de Hidratação' },
    { slug: 'superficie-corporal', name: 'Calculadora de Superfície Corporal' },
    { slug: 'apgar', name: 'Calculadora de Apgar' },
    { slug: 'glasgow', name: 'Escala de Glasgow Pediátrica' },
    { slug: 'capurro', name: 'Método de Capurro' },
    { slug: 'ballard', name: 'Método de Ballard' },
    { slug: 'finnegan', name: 'Escala de Finnegan' },
    { slug: 'bhutani', name: 'Nomograma de Bhutani' },
    { slug: 'rodwell', name: 'Calculadora de Rodwell' },
    { slug: 'gina', name: 'Calculadora GINA' }
  ];

  console.log(`✅ Encontradas ${calculators.length} calculadoras`);
  return calculators;
}

/**
 * Gera lista de rotas para prerendering
 */
async function generateRoutes() {
  console.log('🚀 Iniciando geração de rotas...');

  const routes = [
    // Páginas estáticas principais
    '/',
    '/medicamentos/painel',
    '/medicamentos-amamentacao',
    '/calculadoras',
    '/puericultura',
    '/puericultura/curva-de-crescimento',
    '/puericultura/calendario-vacinal',
    '/puericultura/formulas',
    '/puericultura/suplementacao-infantil',
    '/puericultura/patient-overview',
    '/dnpm',
    '/icd',
    '/condutas-e-manejos',
    '/ai-assistant',
    '/blog',
    '/terms',
    '/privacy',
    '/poisonings'
  ];

  // Buscar medicamentos e adicionar às rotas
  const medications = await fetchMedications();
  medications.forEach(med => {
    if (med.slug) {
      routes.push(`/medicamentos/${med.slug}`);
    }
  });

  // Buscar bulas profissionais e adicionar às rotas
  const professionalLabels = await fetchProfessionalLabels();
  professionalLabels.forEach(label => {
    if (label.slug) {
      routes.push(`/bulas-profissionais/${label.slug}`);
    }
  });

  // Buscar condutas e manejos e adicionar às rotas
  const conducts = await fetchConducts();
  conducts.forEach(conduct => {
    if (conduct.slug) {
      routes.push(`/condutas-e-manejos/${conduct.slug}`);
    }
  });

  // Buscar calculadoras e adicionar às rotas
  const calculators = await fetchCalculators();
  calculators.forEach(calc => {
    routes.push(`/calculadoras/${calc.slug}`);
  });

  // Buscar medicamentos na amamentação e adicionar às rotas
  const breastfeedingMedications = await fetchBreastfeedingMedications();
  breastfeedingMedications.forEach(med => {
    if (med.slug) {
      routes.push(`/medicamentos-amamentacao/${med.slug}`);
    }
  });

  // Adicionar rotas de fluxogramas
  const flowcharts = [
    'asma',
    'anafilaxia',
    'convulsao',
    'cetoacidose',
    'dengue',
    'pecarn',
    'animais-peconhentos/botrópico',
    'animais-peconhentos/crotálico',
    'animais-peconhentos/elapídico',
    'animais-peconhentos/loxoscélico',
    'animais-peconhentos/phoneutria',
    'animais-peconhentos/escorpiao'
  ];

  flowcharts.forEach(flow => {
    routes.push(`/fluxogramas/${flow}`);
  });

  // Adicionar rotas de intoxicações
  const poisonings = [
    'benzodiazepinicos',
    'opioides',
    'anticolinergicos',
    'simpatomimeticos',
    'colinergicos',
    'metemoglobinemia',
    'paracetamol',
    'antidepressivos_triciclicos',
    'betabloqueadores'
  ];

  poisonings.forEach(poisoning => {
    routes.push(`/poisonings/${poisoning}`);
  });

  console.log(`✅ Total de rotas geradas: ${routes.length}`);

  // Salvar rotas em arquivo JSON
  const routesData = {
    routes,
    medications: medications.map(med => ({
      slug: med.slug,
      name: med.name,
      description: med.description,
      category: med.pedbook_medication_categories?.name
    })),
    professionalLabels: professionalLabels.map(label => ({
      slug: label.slug,
      title: label.title,
      content: label.content?.substring(0, 200) + '...' // Resumo do conteúdo
    })),
    conducts: conducts.map(conduct => ({
      slug: conduct.slug,
      title: conduct.title,
      content: conduct.content?.substring(0, 200) + '...' // Resumo do conteúdo
    })),
    breastfeedingMedications: breastfeedingMedications.map(med => ({
      slug: med.slug,
      name: med.name,
      compatibility_level: med.compatibility_level,
      usage_description: med.usage_description,
      section_name: med.section_name,
      subsection_name: med.subsection_name
    })),
    calculators,
    generatedAt: new Date().toISOString(),
    totalRoutes: routes.length
  };

  const outputPath = path.join(process.cwd(), 'prerender-routes.json');
  fs.writeFileSync(outputPath, JSON.stringify(routesData, null, 2));

  console.log(`📄 Rotas salvas em: ${outputPath}`);
  console.log('🎉 Geração de rotas concluída!');

  return routes;
}

/**
 * Gera sitemap.xml dinâmico
 */
async function generateSitemap(routes) {
  console.log('🗺️ Gerando sitemap.xml...');

  const baseUrl = 'https://pedb.com.br';
  const currentDate = new Date().toISOString().split('T')[0];

  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

  routes.forEach(route => {
    let priority = '0.5';
    let changefreq = 'monthly';

    // Definir prioridades específicas
    if (route === '/') {
      priority = '1.0';
      changefreq = 'weekly';
    } else if (route.includes('/medicamentos/')) {
      priority = '0.9';
      changefreq = 'weekly';
    } else if (route.includes('/medicamentos-amamentacao/')) {
      priority = '0.9';
      changefreq = 'weekly';
    } else if (route === '/medicamentos-amamentacao') {
      priority = '0.9';
      changefreq = 'weekly';
    } else if (route.includes('/bulas-profissionais/')) {
      priority = '0.8';
      changefreq = 'monthly';
    } else if (route.includes('/condutas-e-manejos/')) {
      priority = '0.8';
      changefreq = 'monthly';
    } else if (route.includes('/calculadoras/')) {
      priority = '0.8';
      changefreq = 'monthly';
    } else if (route.includes('/puericultura/')) {
      priority = '0.7';
      changefreq = 'monthly';
    } else if (route.includes('/poisonings/')) {
      priority = '0.8';
      changefreq = 'monthly';
    } else if (route.includes('/fluxogramas/')) {
      priority = '0.7';
      changefreq = 'monthly';
    } else if (route === '/icd') {
      priority = '0.6';
      changefreq = 'monthly';
    }

    sitemap += `
  <url>
    <loc>${baseUrl}${route}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>${changefreq}</changefreq>
    <priority>${priority}</priority>
  </url>`;
  });

  sitemap += `
</urlset>`;

  const sitemapPath = path.join(process.cwd(), 'public', 'sitemap.xml');
  fs.writeFileSync(sitemapPath, sitemap);

  console.log(`✅ Sitemap gerado: ${sitemapPath}`);
  console.log(`📊 Total de URLs no sitemap: ${routes.length}`);
}

// Executar sempre quando o script for chamado
console.log('🚀 Testando geração de rotas...');
generateRoutes()
  .then(routes => {
    console.log(`✅ Rotas geradas: ${routes.length}`);
    return generateSitemap(routes);
  })
  .then(() => {
    console.log('🎉 Teste concluído!');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Erro:', error);
    console.error(error.stack);
    process.exit(1);
  });

export { generateRoutes, generateSitemap, fetchMedications, fetchCalculators };
