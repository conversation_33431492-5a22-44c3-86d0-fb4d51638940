import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Filter,
  Calendar,
  Tag,
  TrendingUp,
  Clock,
  Star,
  Search,
  X,
  Droplets
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useNewsCategories } from '@/hooks/useNewsletters';
import { cn } from '@/lib/utils';

interface PediDropFiltersProps {
  onFilterChange: (filters: FilterState) => void;
  currentFilters: FilterState;
}

export interface FilterState {
  search: string;
  category: string;
  sortBy: 'recent' | 'trending' | 'popular';
  timeRange: 'today' | 'week' | 'month' | 'all';
}

const sortOptions = [
  { value: 'recent', label: 'Mais Recentes', icon: Clock },
  { value: 'trending', label: 'Em Alta', icon: TrendingUp },
  { value: 'popular', label: 'Populares', icon: Star }
];

const timeRangeOptions = [
  { value: 'today', label: 'Hoje' },
  { value: 'week', label: 'Esta Semana' },
  { value: 'month', label: '<PERSON>ste Mês' },
  { value: 'all', label: 'Todos' }
];

export const PediDropFilters: React.FC<PediDropFiltersProps> = ({
  onFilterChange,
  currentFilters
}) => {
  const [filters, setFilters] = useState<FilterState>(currentFilters);
  const [isExpanded, setIsExpanded] = useState(false);
  const { data: categories, isLoading: isLoadingCategories } = useNewsCategories();

  const updateFilters = (newFilters: Partial<FilterState>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFilterChange(updatedFilters);
  };

  const clearFilters = () => {
    const clearedFilters: FilterState = {
      search: '',
      category: '',
      sortBy: 'recent',
      timeRange: 'all'
    };
    setFilters(clearedFilters);
    onFilterChange(clearedFilters);
  };

  const hasActiveFilters = filters.search || filters.category || filters.sortBy !== 'recent' || filters.timeRange !== 'all';

  return (
    <div className="bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
      {/* Header dos Filtros */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Droplets className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          <h3 className="font-semibold text-gray-900 dark:text-gray-100">
            Filtrar PediDrops
          </h3>
          {hasActiveFilters && (
            <Badge variant="secondary" className="ml-2">
              Filtros ativos
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <X className="h-4 w-4 mr-1" />
              Limpar
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="lg:hidden"
          >
            <Filter className="h-4 w-4 mr-1" />
            {isExpanded ? 'Ocultar' : 'Filtros'}
          </Button>
        </div>
      </div>

      {/* Busca Principal */}
      <div className="relative mb-4">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        <Input
          placeholder="Buscar por medicamento, condição, protocolo..."
          value={filters.search}
          onChange={(e) => updateFilters({ search: e.target.value })}
          className="pl-10 h-11 bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-base"
        />
      </div>

      {/* Filtros Expandidos */}
      <motion.div
        initial={false}
        animate={{ 
          height: isExpanded || window.innerWidth >= 1024 ? 'auto' : 0,
          opacity: isExpanded || window.innerWidth >= 1024 ? 1 : 0
        }}
        transition={{ duration: 0.3 }}
        className="overflow-hidden lg:overflow-visible"
      >
        <div className="space-y-4">
          {/* Ordenação */}
          <div>
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
              Ordenar por
            </label>
            <div className="flex flex-wrap gap-2">
              {sortOptions.map((option) => {
                const Icon = option.icon;
                return (
                  <Button
                    key={option.value}
                    variant={filters.sortBy === option.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => updateFilters({ sortBy: option.value as FilterState['sortBy'] })}
                    className={cn(
                      "flex items-center gap-1.5",
                      filters.sortBy === option.value && "bg-blue-600 hover:bg-blue-700"
                    )}
                  >
                    <Icon className="h-4 w-4" />
                    {option.label}
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Período */}
          <div>
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
              Período
            </label>
            <div className="flex flex-wrap gap-2">
              {timeRangeOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={filters.timeRange === option.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateFilters({ timeRange: option.value as FilterState['timeRange'] })}
                  className={cn(
                    "flex items-center gap-1.5",
                    filters.timeRange === option.value && "bg-blue-600 hover:bg-blue-700"
                  )}
                >
                  <Calendar className="h-4 w-4" />
                  {option.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Categorias */}
          <div>
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
              Categorias
            </label>
            {isLoadingCategories ? (
              <div className="flex gap-2">
                {Array.from({ length: 4 }).map((_, index) => (
                  <div
                    key={index}
                    className="h-8 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"
                  />
                ))}
              </div>
            ) : (
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={!filters.category ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateFilters({ category: '' })}
                  className={cn(
                    "flex items-center gap-1.5",
                    !filters.category && "bg-blue-600 hover:bg-blue-700"
                  )}
                >
                  <Tag className="h-4 w-4" />
                  Todas
                </Button>
                {categories?.map((category) => (
                  <Button
                    key={category}
                    variant={filters.category === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => updateFilters({ category })}
                    className={cn(
                      "flex items-center gap-1.5",
                      filters.category === category && "bg-blue-600 hover:bg-blue-700"
                    )}
                  >
                    <Tag className="h-4 w-4" />
                    {category}
                  </Button>
                ))}
              </div>
            )}
          </div>

          {/* Filtros Rápidos */}
          <div>
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
              Filtros Rápidos
            </label>
            <div className="flex flex-wrap gap-2">
              {[
                { label: "FDA/ANVISA", search: "FDA ANVISA aprovação" },
                { label: "Protocolos", search: "protocolo guideline" },
                { label: "Posologia", search: "dose posologia" },
                { label: "Pérolas", search: "pérola clínica" }
              ].map((quickFilter) => (
                <Button
                  key={quickFilter.label}
                  variant="outline"
                  size="sm"
                  onClick={() => updateFilters({ search: quickFilter.search })}
                  className="flex items-center gap-1.5 text-xs"
                >
                  <Star className="h-3 w-3" />
                  {quickFilter.label}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Resumo dos Filtros Ativos */}
      {hasActiveFilters && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"
        >
          <div className="flex flex-wrap gap-2 items-center">
            <span className="text-sm text-gray-600 dark:text-gray-400">Filtros ativos:</span>
            {filters.search && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <Search className="h-3 w-3" />
                "{filters.search}"
                <button
                  onClick={() => updateFilters({ search: '' })}
                  className="ml-1 hover:text-red-500"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}
            {filters.category && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <Tag className="h-3 w-3" />
                {filters.category}
                <button
                  onClick={() => updateFilters({ category: '' })}
                  className="ml-1 hover:text-red-500"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}
            {filters.sortBy !== 'recent' && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <TrendingUp className="h-3 w-3" />
                {sortOptions.find(opt => opt.value === filters.sortBy)?.label}
                <button
                  onClick={() => updateFilters({ sortBy: 'recent' })}
                  className="ml-1 hover:text-red-500"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}
            {filters.timeRange !== 'all' && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {timeRangeOptions.find(opt => opt.value === filters.timeRange)?.label}
                <button
                  onClick={() => updateFilters({ timeRange: 'all' })}
                  className="ml-1 hover:text-red-500"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
};
