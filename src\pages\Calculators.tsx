
import { Link, useNavigate } from "react-router-dom";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { Calculator, Baby, Activity, Calendar, Brain, Stethoscope, Wind, Eye, Scale, ArrowLeft, ChartLine, Syringe } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import CalculatorCard from "@/components/calculators/CalculatorCard";
import { Button } from "@/components/ui/button";
import { getThemeClasses } from "@/components/ui/theme-utils";
import { cn } from "@/lib/utils";

const Calculators = () => {
  const navigate = useNavigate();
  const calculators = [
    {
      title: "Nomograma de Bhutani",
      description: "Avaliação do risco de hiperbilirrubinemia significativa em recém-nascidos",
      icon: ChartLine,
      color: "yellow",
      path: "/calculadoras/bhutani",
    },
    {
      title: "Escore de APGAR",
      description: "Avaliação da vitalidade de recém-nascidos baseada em cinco parâmetros clínicos",
      icon: Baby,
      color: "blue",
      path: "/calculadoras/apgar",
    },
    {
      title: "Escore de Rodwell",
      description: "Avaliação da probabilidade de sepse neonatal com base em critérios hematológicos",
      icon: Activity,
      color: "red",
      path: "/calculadoras/rodwell",
    },
    {
      title: "Capurro Somático",
      description: "Avaliação da idade gestacional do recém-nascido com base em características clínicas",
      icon: Calendar,
      color: "green",
      path: "/calculadoras/capurro",
    },
    {
      title: "Capurro Neurológico",
      description: "Avaliação da idade gestacional com base em critérios neurológicos e somáticos",
      icon: Brain,
      color: "purple",
      path: "/calculadoras/capurro-neuro",
    },
    {
      title: "Escala de Finnegan",
      description: "Avaliação da síndrome de abstinência neonatal (SAN) em recém-nascidos",
      icon: Stethoscope,
      color: "orange",
      path: "/calculadoras/finnegan",
    },
    {
      title: "Controle da Asma (GINA)",
      description: "Avaliação do controle da asma baseada nos critérios GINA 2022",
      icon: Wind,
      color: "blue",
      path: "/calculadoras/gina",
    },
    {
      title: "Escala de Glasgow Pediátrica",
      description: "Avaliação do nível de consciência em crianças",
      icon: Eye,
      color: "indigo",
      path: "/calculadoras/glasgow",
    },
    {
      title: "IMC e Obesidade Pediátrica",
      description: "Avaliação do estado nutricional de crianças e adolescentes com base nas tabelas de Z-Score da OMS",
      icon: Scale,
      color: "green",
      path: "/calculadoras/imc",
    },
    {
      title: "Medicações para Intubação Orotraqueal",
      description: "Cálculo automático de doses para pré-medicação, indução e bloqueadores neuromusculares",
      icon: Syringe,
      color: "red",
      path: "/calculadoras/intubacao",
    }
  ];

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col")}>
      <HelmetWrapper>
        <title>PedBook | Calculadoras</title>
        <meta name="description" content="Calculadoras pediátricas para auxílio na prática diária." />
      </HelmetWrapper>

      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate('/')}
              className="hover:bg-primary/10 hidden sm:flex dark:hover:bg-primary/20"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="text-center flex-1 space-y-4">
              <h1 className={getThemeClasses.gradientHeading("text-3xl")}>
                Calculadoras e Escalas Pediátricas
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                Ferramentas de cálculo para auxílio na prática pediátrica diária
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2 sm:gap-4">
            {calculators.map((calculator, index) => (
              <div
                key={index}
                className="transform transition-all duration-500 hover:scale-[1.02]"
                style={{
                  animationDelay: `${index * 100}ms`,
                  animation: 'fade-in-up 0.5s ease-out forwards',
                  opacity: 0
                }}
              >
                <CalculatorCard
                  {...calculator}
                />
              </div>
            ))}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Calculators;
