import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Clock,
  Target,
  Lightbulb,
  AlertTriangle,
  BookOpen,
  Calendar,
  Share2,
  Bookmark,
  ExternalLink,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { MarkdownViewer } from '@/components/ui/MarkdownEditor';

import { type PediDropPost } from '@/hooks/usePediDrop';

interface PediDropData extends PediDropPost {}

interface PediDropCardProps {
  drop: PediDropData;
  index: number;
  variant?: 'standard' | 'featured' | 'compact';
  isToday?: boolean;
}

export const PediDropCard: React.FC<PediDropCardProps> = ({
  drop,
  index,
  variant = 'standard',
  isToday = false
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleShare = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (navigator.share) {
      navigator.share({
        title: drop.title,
        text: drop.summary,
        url: window.location.href
      });
    }
  };

  const handleBookmark = (e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: Implementar sistema de favoritos
    console.log('Bookmark:', drop.id);
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.5, 
        delay: index * 0.1 
      }
    }
  };

  if (variant === 'compact') {
    return (
      <motion.div
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        whileHover={{ y: -2 }}
        transition={{ duration: 0.2 }}
      >
        <Card className="overflow-hidden hover:shadow-lg transition-all duration-300 border-l-4 border-l-blue-500">
          <CardContent className="p-4">
            <div className="flex items-start justify-between gap-3">
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 line-clamp-2 mb-2">
                  {drop.title}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
                  {drop.summary}
                </p>
                <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {formatDate(drop.pub_date)}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {drop.reading_time || 5} min
                  </div>
                </div>
              </div>
              <div className="flex flex-col gap-1">
                <Button variant="ghost" size="sm" onClick={handleBookmark}>
                  <Bookmark className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" onClick={handleShare}>
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  if (variant === 'featured') {
    return (
      <motion.div
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        whileHover={{ y: -2 }}
        transition={{ duration: 0.3 }}
        className="mb-8 max-w-4xl mx-auto"
      >
        {/* Header - inspirado no design */}
        <div className="bg-gray-800 dark:bg-gray-900 text-white text-center py-3 rounded-t-lg">
          <h2 className="text-sm font-bold tracking-wider uppercase">
            {isToday ? 'PEDIDROP DE HOJE' : 'PEDIDROP'}
          </h2>
        </div>

        {/* Card principal */}
        <div className="bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700 rounded-b-lg shadow-lg">
          <div className="p-6">
            {/* Header com título e metadados */}
            <div className="flex items-start justify-between gap-4 mb-6">
              <div className="flex-1 min-w-0">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 leading-tight">
                  {drop.title}
                </h1>
              </div>

              {/* Metadados compactos */}
              <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 px-3 py-2 rounded-lg shrink-0">
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {drop.reading_time || 5} min
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {formatDate(drop.pub_date)}
                </div>
              </div>
            </div>

            {/* Resumo principal */}
            <div className="text-gray-700 dark:text-gray-300 text-base leading-relaxed mb-8">
              <MarkdownViewer content={drop.summary} />
            </div>

            {/* Seções Dinâmicas - estilo mais limpo */}
            {drop.sections && drop.sections.length > 0 && (
              <div className="space-y-6">
                {drop.sections.map((section, idx) => (
                  <div key={section.id} className="mb-6">
                    <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-3">
                      • {section.title}
                    </h3>
                    <div className="text-gray-700 dark:text-gray-300 leading-relaxed pl-4">
                      <MarkdownViewer content={section.content} />
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Referências - estilo mais limpo com links */}
            {drop.references && drop.references.length > 0 && (
              <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-600">
                <h3 className="font-bold text-gray-800 dark:text-gray-200 mb-4">
                  📚 Referências
                </h3>
                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-3">
                  {drop.references.map((ref, idx) => (
                    <li key={ref.id} className="flex items-start gap-2">
                      <span className="text-gray-400 mt-1 font-medium">{idx + 1}.</span>
                      <div className="flex-1">
                        {ref.url ? (
                          <a
                            href={ref.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:underline transition-colors"
                          >
                            {ref.title || ref.reference_text}
                          </a>
                        ) : (
                          <span>{ref.title || ref.reference_text}</span>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Botões de ação */}
            <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-600 flex gap-3">
              <Button variant="outline" size="sm" onClick={handleBookmark}>
                <Bookmark className="h-4 w-4 mr-2" />
                Salvar
              </Button>
              <Button variant="outline" size="sm" onClick={handleShare}>
                <Share2 className="h-4 w-4 mr-2" />
                Compartilhar
              </Button>
            </div>
          </div>
        </div>
      </motion.div>
    );
  }

  // Standard variant - Versão compacta para PediDrops Anteriores
  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover={{ y: -1 }}
      transition={{ duration: 0.3 }}
      className="border border-gray-200 dark:border-gray-600 rounded-lg mb-4 overflow-hidden"
    >
      {/* Header clicável */}
      <div
        className="flex items-start justify-between gap-4 p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex-1 min-w-0">
          {/* Título chamativo */}
          <h3 className="font-bold text-lg text-gray-900 dark:text-gray-100 leading-tight mb-2">
            • {drop.title}
          </h3>

          {/* Metadados e botões de ação */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {drop.reading_time || 5} min
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {formatDate(drop.pub_date)}
              </div>
            </div>

            {/* Botões de ação */}
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  handleBookmark(e);
                }}
                className="h-6 w-6 p-0"
              >
                <Bookmark className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  handleShare(e);
                }}
                className="h-6 w-6 p-0"
              >
                <Share2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* Botão de expandir */}
        <div className="shrink-0">
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Conteúdo expandido */}
      {isExpanded && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className="border-t border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-800"
        >
          <div className="p-4">
            {/* Resumo */}
            <div className="mb-6 pb-4 border-b border-gray-200 dark:border-gray-600">
              <p className="text-gray-600 dark:text-gray-400 leading-relaxed text-sm">
                {drop.summary}
              </p>
            </div>

            {/* Seções */}
            {drop.sections && drop.sections.length > 0 && (
              <div className="space-y-4 mb-6">
                {drop.sections.map((section, idx) => (
                  <div key={section.id} className="mb-4">
                    <h4 className="text-base font-bold text-gray-900 dark:text-gray-100 mb-2">
                      • {section.title}
                    </h4>
                    <div className="text-gray-700 dark:text-gray-300 leading-relaxed pl-4 text-sm">
                      <MarkdownViewer content={section.content} />
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Referências */}
            {drop.references && drop.references.length > 0 && (
              <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
                <h4 className="font-bold text-gray-800 dark:text-gray-200 mb-3 text-sm">
                  📚 Referências
                </h4>
                <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-2">
                  {drop.references.map((ref, idx) => (
                    <li key={ref.id} className="flex items-start gap-2">
                      <span className="text-gray-400 mt-1 font-medium">{idx + 1}.</span>
                      <div className="flex-1">
                        {ref.url ? (
                          <a
                            href={ref.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:underline transition-colors"
                          >
                            {ref.title || ref.reference_text}
                          </a>
                        ) : (
                          <span>{ref.title || ref.reference_text}</span>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};
