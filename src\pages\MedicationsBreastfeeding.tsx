import React, { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Link } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Baby, Search, Filter, ChevronDown, X, Folder,
  FolderOpen, ChevronRight, Info, ArrowUpDown, Plus, Minus,
  Home, ArrowLeft, Pill, AlertTriangle, CheckCircle, XCircle,
  Heart, Shield, Users, TrendingUp, Clock, Star, Zap
} from "lucide-react";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { supabase } from "@/integrations/supabase/client";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";

// Hooks e componentes otimizados
import {
  useBreastfeedingStructure,
  useBreastfeedingSearch,
  useBreastfeedingPrefetch
} from "@/hooks/useBreastfeedingData";
import {
  BreastfeedingSkeletons,
  ProgressIndicator,
  LoadingMessages
} from "@/components/breastfeeding/BreastfeedingSkeletons";

// Tipos de dados
interface Medication {
  id: string;
  name: string;
  compatibility_level: string;
  usage_description: string;
  additional_info?: string;
  efeitos_no_lactente?: string;
  alternativas_seguras?: string[];
  orientacoes_uso?: string;
  section_name?: string;
  subsection_name?: string;
}

// Interface para resultados de busca (pode ter campos adicionais da RPC)
interface SearchResult {
  id: string;
  name: string;
  compatibility_level?: string;
  usage_description?: string;
  additional_info?: string;
  efeitos_no_lactente?: string;
  alternativas_seguras?: string[];
  orientacoes_uso?: string;
  section_name?: string;
  subsection_name?: string;
  search_rank?: number;
  [key: string]: any; // Para campos adicionais da RPC
}

interface Subsection {
  id: string;
  name: string;
  description?: string;
  medications?: Medication[];
  subsections?: Subsection[];
}

interface Section {
  id: string;
  name: string;
  description?: string;
  medications?: Medication[];
  subsections?: Subsection[];
}

interface BreastfeedingData {
  sections: Section[];
}

interface NavigationState {
  currentLevel: 'sections' | 'subsections' | 'medications';
  currentSectionId: string | null;
  currentSubsectionId: string | null;
  breadcrumbs: Array<{id: string, name: string, type: 'section' | 'subsection'}>;
}

// Componente principal
const MedicationsBreastfeeding: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [activeFilter, setActiveFilter] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});
  const [expandedSubsections, setExpandedSubsections] = useState<Record<string, boolean>>({});
  const [navigation, setNavigation] = useState<NavigationState>({
    currentLevel: 'sections',
    currentSectionId: null,
    currentSubsectionId: null,
    breadcrumbs: []
  });
  const { toast } = useToast();

  // Função para obter a cor com base no nível de compatibilidade
  const getCompatibilityColor = (level: string): string => {
    if (!level) return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-300';
    switch (level.toLowerCase()) {
      case 'verde':
        return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300';
      case 'amarelo':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'vermelho':
        return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  const getCompatibilityIcon = (level: string): JSX.Element => {
    if (!level) return <Pill className="h-3.5 w-3.5 text-gray-500 mr-1.5" />;
    switch (level.toLowerCase()) {
      case 'verde':
        return <CheckCircle className="h-3.5 w-3.5 text-green-500 mr-1.5" />;
      case 'amarelo':
        return <AlertTriangle className="h-3.5 w-3.5 text-yellow-500 mr-1.5" />;
      case 'vermelho':
        return <XCircle className="h-3.5 w-3.5 text-red-500 mr-1.5" />;
      default:
        return <Pill className="h-3.5 w-3.5 text-gray-500 mr-1.5" />;
    }
  };

  // Usar hook otimizado com view materializada
  const { data: breastfeedingData, isLoading, error, refetch } = useBreastfeedingStructure();

  // Consulta para buscar medicamentos por pesquisa com debounce
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Usar hook de busca otimizada
  const { data: searchResults = [], isLoading: isSearching, error: searchError } = useBreastfeedingSearch(debouncedSearchQuery) as { data: SearchResult[], isLoading: boolean, error: any };

  // Hook para prefetch inteligente
  const { prefetchSubsections, prefetchMedications } = useBreastfeedingPrefetch();

  // Filtrar medicamentos por compatibilidade
  const toggleFilter = (level: string) => {
    setActiveFilter(activeFilter === level ? null : level);
  };

  // Gerenciar exibição de seção
  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  // Gerenciar exibição de subseção
  const toggleSubsection = (subsectionId: string) => {
    setExpandedSubsections(prev => ({
      ...prev,
      [subsectionId]: !prev[subsectionId]
    }));
  };

  // Verificar recursivamente se um medicamento corresponde ao filtro
  const medicationMatchesFilter = (medication: Medication): boolean => {
    if (!activeFilter) return true;
    if (!medication?.compatibility_level) return false;
    return medication.compatibility_level.toLowerCase() === activeFilter.toLowerCase();
  };

  // Verificar se uma seção tem medicamentos visíveis após filtragem
  const sectionHasVisibleMedications = (section: Section): boolean => {
    // Calcular total de medicamentos na seção
    const totalMedications = (section.medications?.length || 0) +
      (section.subsections?.reduce((acc, sub) => acc + (sub.medications?.length || 0), 0) || 0);

    // Se não há medicamentos, não mostrar a seção
    if (totalMedications === 0) {
      return false;
    }

    // Se não há filtro ativo, mostrar seções que têm medicamentos
    if (!activeFilter) {
      return true;
    }

    // Verificar medicamentos na própria seção
    if (section.medications?.some(med => medicationMatchesFilter(med))) {
      return true;
    }

    // Verificar medicamentos nas subseções
    if (section.subsections?.some(sub => subsectionHasVisibleMedications(sub))) {
      return true;
    }

    return false;
  };

  // Verificar se uma subseção tem medicamentos visíveis após filtragem (recursivamente)
  const subsectionHasVisibleMedications = (subsection: Subsection): boolean => {
    // Calcular total de medicamentos na subseção
    const totalMedications = (subsection.medications?.length || 0) +
      (subsection.subsections?.reduce((acc, sub) => acc + (sub.medications?.length || 0), 0) || 0);

    // Se não há medicamentos, não mostrar a subseção
    if (totalMedications === 0) {
      return false;
    }

    // Se não há filtro ativo, mostrar subseções que têm medicamentos
    if (!activeFilter) {
      return true;
    }

    // Verificar medicamentos na própria subseção
    if (subsection.medications && subsection.medications.some(med => medicationMatchesFilter(med))) {
      return true;
    }

    // Verificar medicamentos nas subseções aninhadas
    if (subsection.subsections && subsection.subsections.some(sub => subsectionHasVisibleMedications(sub))) {
      return true;
    }

    return false;
  };

  // Componente para o Dialog de detalhes do medicamento
  const MedicationDetailsDialog = ({ medication }: { medication: Medication }) => {
    const getCompatibilityStyles = (level: string) => {
      if (!level) {
        return {
          headerBg: 'bg-gradient-to-r from-gray-500 to-gray-600',
          icon: <Pill className="h-6 w-6 text-white" />,
          badge: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
        };
      }
      switch (level.toLowerCase()) {
        case 'verde':
          return {
            headerBg: 'bg-gradient-to-r from-green-500 to-green-600',
            icon: <Shield className="h-6 w-6 text-white" />,
            badge: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
          };
        case 'amarelo':
          return {
            headerBg: 'bg-gradient-to-r from-yellow-500 to-yellow-600',
            icon: <AlertTriangle className="h-6 w-6 text-white" />,
            badge: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
          };
        case 'vermelho':
          return {
            headerBg: 'bg-gradient-to-r from-red-500 to-red-600',
            icon: <XCircle className="h-6 w-6 text-white" />,
            badge: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
          };
        default:
          return {
            headerBg: 'bg-gradient-to-r from-gray-500 to-gray-600',
            icon: <Pill className="h-6 w-6 text-white" />,
            badge: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
          };
      }
    };

    const styles = getCompatibilityStyles(medication.compatibility_level);

    return (
      <DialogContent className="max-w-2xl rounded-2xl max-h-[85dvh] overflow-hidden p-0 gap-0" hideCloseButton>
        {/* Header com gradiente */}
        <div className={cn("relative p-6 text-white", styles.headerBg)}>
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative flex items-start justify-between">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/20 backdrop-blur-sm rounded-full">
                {styles.icon}
              </div>
              <div>
                <DialogTitle className="text-2xl font-bold text-white mb-2">
                  {medication.name}
                </DialogTitle>
                <Badge className={cn("text-sm font-medium", styles.badge)}>
                  {medication.compatibility_level || 'Não definido'}
                </Badge>
              </div>
            </div>
            <DialogClose asChild>
              <Button variant="ghost" size="icon" className="text-white/80 hover:text-white hover:bg-white/20 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
                <X className="h-5 w-5" />
              </Button>
            </DialogClose>
          </div>
        </div>

        {/* Conteúdo */}
        <div className="overflow-y-auto max-h-[calc(85dvh-140px)] p-6 pb-8 md:pb-6 space-y-6">
          {/* Descrição principal */}
          <div className="bg-blue-50/50 dark:bg-blue-950/20 p-4 rounded-xl border border-blue-100 dark:border-blue-900/20">
            <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2 flex items-center gap-2">
              <Info className="h-5 w-5" />
              Uso na Amamentação
            </h3>
            <p className="text-blue-800 dark:text-blue-200 leading-relaxed">
              {medication.usage_description}
            </p>
          </div>

          {medication.additional_info && (
            <div className="bg-gray-50/50 dark:bg-gray-950/20 p-4 rounded-xl border border-gray-100 dark:border-gray-800">
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2 flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Informações Adicionais
              </h3>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed italic">
                {medication.additional_info}
              </p>
            </div>
          )}

          {medication.efeitos_no_lactente && (
            <div className="bg-pink-50/50 dark:bg-pink-950/20 p-4 rounded-xl border border-pink-100 dark:border-pink-900/20">
              <h3 className="font-semibold text-pink-900 dark:text-pink-100 mb-2 flex items-center gap-2">
                <Heart className="h-5 w-5" />
                Efeitos no Lactente
              </h3>
              <p className="text-pink-800 dark:text-pink-200 leading-relaxed">
                {medication.efeitos_no_lactente}
              </p>
            </div>
          )}

          {medication.alternativas_seguras && medication.alternativas_seguras.length > 0 && (
            <div className="bg-green-50/50 dark:bg-green-950/20 p-4 rounded-xl border border-green-100 dark:border-green-900/20">
              <h3 className="font-semibold text-green-900 dark:text-green-100 mb-3 flex items-center gap-2">
                <Star className="h-5 w-5" />
                Alternativas Seguras
              </h3>
              <div className="flex flex-wrap gap-2">
                {medication.alternativas_seguras.map((alt, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 border border-green-200 dark:border-green-800"
                  >
                    <CheckCircle className="h-3 w-3 mr-1.5" />
                    {alt}
                  </span>
                ))}
              </div>
            </div>
          )}

          {medication.orientacoes_uso && (
            <div className="bg-amber-50/50 dark:bg-amber-950/20 p-4 rounded-xl border border-amber-100 dark:border-amber-900/20">
              <h3 className="font-semibold text-amber-900 dark:text-amber-100 mb-2 flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Orientações de Uso
              </h3>
              <p className="text-amber-800 dark:text-amber-200 leading-relaxed">
                {medication.orientacoes_uso}
              </p>
            </div>
          )}
        </div>


      </DialogContent>
    );
  };

  // Renderizar medicamento com design moderno
  const renderMedication = (medication: Medication) => {
    if (!medicationMatchesFilter(medication)) {
      return null;
    }

    const getCompatibilityStyles = (level: string) => {
      if (!level) {
        return {
          border: 'border-gray-200 dark:border-gray-700',
          bg: 'bg-gray-50/50 dark:bg-gray-950/20',
          icon: 'text-gray-600 dark:text-gray-400',
          badge: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
        };
      }
      switch (level.toLowerCase()) {
        case 'verde':
          return {
            border: 'border-green-200 dark:border-green-800',
            bg: 'bg-green-50/50 dark:bg-green-950/20',
            icon: 'text-green-600 dark:text-green-400',
            badge: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
          };
        case 'amarelo':
          return {
            border: 'border-yellow-200 dark:border-yellow-800',
            bg: 'bg-yellow-50/50 dark:bg-yellow-950/20',
            icon: 'text-yellow-600 dark:text-yellow-400',
            badge: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
          };
        case 'vermelho':
          return {
            border: 'border-red-200 dark:border-red-800',
            bg: 'bg-red-50/50 dark:bg-red-950/20',
            icon: 'text-red-600 dark:text-red-400',
            badge: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
          };
        default:
          return {
            border: 'border-gray-200 dark:border-gray-700',
            bg: 'bg-gray-50/50 dark:bg-gray-950/20',
            icon: 'text-gray-600 dark:text-gray-400',
            badge: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
          };
      }
    };

    const styles = getCompatibilityStyles(medication.compatibility_level);

    return (
      <Dialog key={medication.id}>
        <DialogTrigger asChild>
          <Card
            className={cn(
              "group cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1",
              "border-2 rounded-xl overflow-hidden",
              styles.border,
              styles.bg
            )}
          >
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  <div className={cn("p-2 rounded-full bg-white/80 dark:bg-gray-800/80", styles.icon)}>
                    {medication.compatibility_level?.toLowerCase() === 'verde' && <Shield className="h-5 w-5" />}
                    {medication.compatibility_level?.toLowerCase() === 'amarelo' && <AlertTriangle className="h-5 w-5" />}
                    {medication.compatibility_level?.toLowerCase() === 'vermelho' && <XCircle className="h-5 w-5" />}
                    {!medication.compatibility_level && <Pill className="h-5 w-5" />}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-primary transition-colors">
                      {medication.name}
                    </h3>
                    <Badge className={cn("text-xs mt-1", styles.badge)}>
                      {medication.compatibility_level || 'Não definido'}
                    </Badge>
                  </div>
                </div>
                <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-primary transition-colors" />
              </div>

              <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 leading-relaxed">
                {medication.usage_description}
              </p>

              {(medication.efeitos_no_lactente || medication.alternativas_seguras?.length || medication.orientacoes_uso) && (
                <div className="flex items-center gap-2 mt-3 pt-3 border-t border-gray-200/50 dark:border-gray-700/50">
                  {medication.efeitos_no_lactente && (
                    <div className="flex items-center gap-1 text-xs text-pink-600 dark:text-pink-400">
                      <Heart className="h-3 w-3" />
                      <span>Efeitos</span>
                    </div>
                  )}
                  {medication.alternativas_seguras?.length && (
                    <div className="flex items-center gap-1 text-xs text-green-600 dark:text-green-400">
                      <Star className="h-3 w-3" />
                      <span>Alternativas</span>
                    </div>
                  )}
                  {medication.orientacoes_uso && (
                    <div className="flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400">
                      <Info className="h-3 w-3" />
                      <span>Orientações</span>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </DialogTrigger>
        <MedicationDetailsDialog medication={medication} />
      </Dialog>
    );
  };

  // Renderizar card de seção moderna
  const renderSectionCard = (section: Section) => {
    const medicationCount = (section.medications?.length || 0) +
      (section.subsections?.reduce((acc, sub) => acc + (sub.medications?.length || 0), 0) || 0);

    const compatibilityStats = {
      verde: (section.medications?.filter(m => m.compatibility_level?.toLowerCase() === 'verde').length || 0) +
        (section.subsections?.reduce((acc, sub) =>
          acc + (sub.medications?.filter(m => m.compatibility_level?.toLowerCase() === 'verde').length || 0), 0) || 0),
      amarelo: (section.medications?.filter(m => m.compatibility_level?.toLowerCase() === 'amarelo').length || 0) +
        (section.subsections?.reduce((acc, sub) =>
          acc + (sub.medications?.filter(m => m.compatibility_level?.toLowerCase() === 'amarelo').length || 0), 0) || 0),
      vermelho: (section.medications?.filter(m => m.compatibility_level?.toLowerCase() === 'vermelho').length || 0) +
        (section.subsections?.reduce((acc, sub) =>
          acc + (sub.medications?.filter(m => m.compatibility_level?.toLowerCase() === 'vermelho').length || 0), 0) || 0)
    };

    return (
      <Card
        key={section.id}
        className="group cursor-pointer transition-all duration-200 hover:shadow-xl hover:-translate-y-2 border-2 border-gray-200 dark:border-gray-700 hover:border-pink-300 dark:hover:border-pink-600 rounded-2xl overflow-hidden bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-800 dark:to-gray-900/50"
        onClick={() => navigateToSection(section)}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-pink-100 dark:bg-pink-900/30 rounded-xl group-hover:bg-pink-200 dark:group-hover:bg-pink-800/40 transition-colors">
                <Folder className="h-6 w-6 text-pink-600 dark:text-pink-400" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100 group-hover:text-pink-600 dark:group-hover:text-pink-400 transition-colors">
                  {section.name}
                </CardTitle>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  {medicationCount} medicamento(s)
                </p>
              </div>
            </div>
            <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-pink-500 transition-colors" />
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {section.description && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
              {section.description}
            </p>
          )}

          {/* Estatísticas de compatibilidade - mostra apenas o filtro ativo ou todos se não houver filtro */}
          <div className="flex items-center gap-3 pt-3 border-t border-gray-100 dark:border-gray-700">
            {(!activeFilter || activeFilter === 'verde') && compatibilityStats.verde > 0 && (
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-xs text-green-600 dark:text-green-400 font-medium">
                  {compatibilityStats.verde}
                </span>
              </div>
            )}
            {(!activeFilter || activeFilter === 'amarelo') && compatibilityStats.amarelo > 0 && (
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span className="text-xs text-yellow-600 dark:text-yellow-400 font-medium">
                  {compatibilityStats.amarelo}
                </span>
              </div>
            )}
            {(!activeFilter || activeFilter === 'vermelho') && compatibilityStats.vermelho > 0 && (
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-xs text-red-600 dark:text-red-400 font-medium">
                  {compatibilityStats.vermelho}
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  // Funções de navegação
  const navigateToSection = (section: Section) => {
    setNavigation({
      currentLevel: 'subsections',
      currentSectionId: section.id,
      currentSubsectionId: null,
      breadcrumbs: [{ id: section.id, name: section.name, type: 'section' }]
    });
  };

  const navigateToSubsection = (section: Section, subsection: Subsection) => {
    setNavigation({
      currentLevel: 'medications',
      currentSectionId: section.id,
      currentSubsectionId: subsection.id,
      breadcrumbs: [
        { id: section.id, name: section.name, type: 'section' },
        { id: subsection.id, name: subsection.name, type: 'subsection' }
      ]
    });
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-pink-50 via-white to-pink-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800">
      <HelmetWrapper>
        <title>Medicamentos e Amamentação | PedBook</title>
        <meta
          name="description"
          content="Informações sobre segurança de medicamentos durante a amamentação para profissionais de saúde."
        />
      </HelmetWrapper>

      <Header />

      {/* Hero Section Redesenhado */}
      <div className="relative bg-gradient-to-r from-pink-500 via-pink-600 to-rose-600 dark:from-pink-800 dark:via-pink-900 dark:to-rose-900">
        <div className="absolute inset-0 bg-black/10 dark:bg-black/20"></div>
        <div className="relative container mx-auto px-4 py-8 md:py-12">
          <div className="max-w-4xl mx-auto">
            {/* Title and Navigation */}
            <div className="space-y-6 mb-8 md:mb-12">
              <div className="flex items-center justify-center gap-3 mb-4">
                {/* Botão de voltar - lado esquerdo */}
                <Link to="/" className="absolute left-4 md:left-0">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-10 w-10 md:h-12 md:w-12 text-white/80 hover:text-white hover:bg-white/10 backdrop-blur-sm transition-colors"
                    aria-label="Voltar ao menu inicial"
                  >
                    <ArrowLeft className="h-5 w-5 md:h-6 md:w-6" />
                  </Button>
                </Link>

                {/* Ícone e Título - centro */}
                <div className="flex items-center gap-3">
                  <div className="p-2 md:p-3 bg-white/20 backdrop-blur-sm rounded-full">
                    <Baby className="h-6 w-6 md:h-8 md:w-8 text-white" />
                  </div>
                  <h1 className="text-2xl md:text-4xl lg:text-5xl font-bold text-white text-center">
                    Medicamentos e Amamentação
                  </h1>
                </div>
              </div>
              <p className="text-lg md:text-xl text-pink-100 max-w-3xl mx-auto leading-relaxed text-center">
                Informações seguras e atualizadas sobre o uso de medicamentos durante o período de amamentação
              </p>
            </div>



            {/* Search Bar Otimizada */}
            <div className="max-w-2xl mx-auto">
              <div className="relative">
                <Search className="absolute left-3 md:left-4 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4 md:h-5 md:w-5" />
                <Input
                  placeholder="Pesquisar medicamento..."
                  className="pl-10 md:pl-12 pr-10 md:pr-12 h-11 md:h-14 text-base md:text-lg bg-white/95 backdrop-blur-sm border-0 shadow-lg rounded-xl focus:ring-2 focus:ring-white/50"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                {searchQuery && (
                  <button
                    className="absolute right-3 md:right-4 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    onClick={() => setSearchQuery("")}
                  >
                    <X className="h-4 w-4 md:h-5 md:w-5" />
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">

          {/* Filtros Ultra Compactos para Mobile */}
          <div className="mb-8">
            <div className="flex items-center justify-center gap-1 sm:gap-2 md:gap-3 px-2">
              <Button
                variant={activeFilter === 'verde' ? 'default' : 'outline'}
                size="sm"
                className={cn(
                  "h-8 px-2 sm:h-10 sm:px-4 md:h-12 md:px-6 rounded-full transition-all duration-200 shadow-sm hover:shadow-md text-xs sm:text-sm md:text-base flex-shrink-0",
                  activeFilter === 'verde'
                    ? 'bg-green-500 hover:bg-green-600 text-white border-green-500'
                    : 'border-green-200 text-green-700 hover:bg-green-50 dark:border-green-800 dark:text-green-400 dark:hover:bg-green-950/20'
                )}
                onClick={() => toggleFilter('verde')}
              >
                <Shield className="h-3 w-3 sm:h-4 sm:w-4 mr-0.5 sm:mr-2" />
                <span className="hidden sm:inline">Seguros</span>
                <span className="sm:hidden">Seg</span>
                {breastfeedingData && (
                  <Badge variant="secondary" className="ml-0.5 sm:ml-2 bg-white/20 text-current text-xs sm:text-sm px-1 sm:px-2 py-0.5">
                    {breastfeedingData.sections?.reduce((acc, section) =>
                      acc + (section.medications?.filter(m => m.compatibility_level?.toLowerCase() === 'verde').length || 0) +
                      (section.subsections?.reduce((subAcc, sub) =>
                        subAcc + (sub.medications?.filter(m => m.compatibility_level?.toLowerCase() === 'verde').length || 0), 0) || 0), 0) || 0}
                  </Badge>
                )}
              </Button>

              <Button
                variant={activeFilter === 'amarelo' ? 'default' : 'outline'}
                size="sm"
                className={cn(
                  "h-8 px-2 sm:h-10 sm:px-4 md:h-12 md:px-6 rounded-full transition-all duration-200 shadow-sm hover:shadow-md text-xs sm:text-sm md:text-base flex-shrink-0",
                  activeFilter === 'amarelo'
                    ? 'bg-yellow-500 hover:bg-yellow-600 text-white border-yellow-500'
                    : 'border-yellow-200 text-yellow-700 hover:bg-yellow-50 dark:border-yellow-800 dark:text-yellow-400 dark:hover:bg-yellow-950/20'
                )}
                onClick={() => toggleFilter('amarelo')}
              >
                <AlertTriangle className="h-3 w-3 sm:h-4 sm:w-4 mr-0.5 sm:mr-2" />
                <span className="hidden sm:inline">Criterioso</span>
                <span className="sm:hidden">Crit</span>
                {breastfeedingData && (
                  <Badge variant="secondary" className="ml-0.5 sm:ml-2 bg-white/20 text-current text-xs sm:text-sm px-1 sm:px-2 py-0.5">
                    {breastfeedingData.sections?.reduce((acc, section) =>
                      acc + (section.medications?.filter(m => m.compatibility_level?.toLowerCase() === 'amarelo').length || 0) +
                      (section.subsections?.reduce((subAcc, sub) =>
                        subAcc + (sub.medications?.filter(m => m.compatibility_level?.toLowerCase() === 'amarelo').length || 0), 0) || 0), 0) || 0}
                  </Badge>
                )}
              </Button>

              <Button
                variant={activeFilter === 'vermelho' ? 'default' : 'outline'}
                size="sm"
                className={cn(
                  "h-8 px-2 sm:h-10 sm:px-4 md:h-12 md:px-6 rounded-full transition-all duration-200 shadow-sm hover:shadow-md text-xs sm:text-sm md:text-base flex-shrink-0",
                  activeFilter === 'vermelho'
                    ? 'bg-red-500 hover:bg-red-600 text-white border-red-500'
                    : 'border-red-200 text-red-700 hover:bg-red-50 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-950/20'
                )}
                onClick={() => toggleFilter('vermelho')}
              >
                <XCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-0.5 sm:mr-2" />
                <span className="hidden sm:inline">Contraindicados</span>
                <span className="sm:hidden">Contr</span>
                {breastfeedingData && (
                  <Badge variant="secondary" className="ml-0.5 sm:ml-2 bg-white/20 text-current text-xs sm:text-sm px-1 sm:px-2 py-0.5">
                    {breastfeedingData.sections?.reduce((acc, section) =>
                      acc + (section.medications?.filter(m => m.compatibility_level?.toLowerCase() === 'vermelho').length || 0) +
                      (section.subsections?.reduce((subAcc, sub) =>
                        subAcc + (sub.medications?.filter(m => m.compatibility_level?.toLowerCase() === 'vermelho').length || 0), 0) || 0), 0) || 0}
                  </Badge>
                )}
              </Button>

              {activeFilter && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-2 sm:h-10 sm:px-4 md:h-12 md:px-6 rounded-full text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 text-xs sm:text-sm md:text-base flex-shrink-0"
                  onClick={() => setActiveFilter(null)}
                >
                  <X className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline ml-1 sm:ml-2">Limpar</span>
                </Button>
              )}
            </div>
          </div>

          {/* Conteúdo principal */}
          {isLoading ? (
            <BreastfeedingSkeletons type="structure" />
          ) : error ? (
            <div className="p-12 text-center">
              <Card className="max-w-md mx-auto border-red-200 dark:border-red-800">
                <CardContent className="p-8">
                  <XCircle className="h-16 w-16 mx-auto text-red-400 mb-4" />
                  <h3 className="font-semibold text-lg text-red-600 dark:text-red-400 mb-2">
                    Erro ao carregar dados
                  </h3>
                  <p className="text-sm text-red-500 dark:text-red-400 mb-6">
                    Não foi possível carregar as informações sobre medicamentos e amamentação.
                  </p>
                  <Button
                    variant="outline"
                    className="border-red-200 text-red-600 dark:border-red-800 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30"
                    onClick={() => {
                      refetch();
                    }}
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Tentar novamente
                  </Button>
                </CardContent>
              </Card>
            </div>
          ) : !breastfeedingData?.sections || breastfeedingData.sections.length === 0 ? (
            <div className="p-12 text-center">
              <Card className="max-w-md mx-auto border-amber-200 dark:border-amber-800">
                <CardContent className="p-8">
                  <AlertTriangle className="h-16 w-16 mx-auto text-amber-400 mb-4" />
                  <h3 className="font-semibold text-lg text-amber-600 dark:text-amber-400 mb-2">
                    Dados não disponíveis
                  </h3>
                  <p className="text-sm text-amber-600 dark:text-amber-400">
                    As informações sobre medicamentos e amamentação ainda não foram carregadas no sistema.
                  </p>
                </CardContent>
              </Card>
            </div>
          ) : (
            <>
              {/* Resultados da pesquisa */}
              {searchQuery.length >= 3 && (
                <div className="mb-8">
                  <Card className="border-blue-200 dark:border-blue-800 bg-blue-50/30 dark:bg-blue-950/20">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-3 text-blue-900 dark:text-blue-100">
                        <Search className="h-5 w-5" />
                        Resultados da Pesquisa
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {isSearching || (searchQuery !== debouncedSearchQuery) ? (
                        <BreastfeedingSkeletons type="search" count={3} />
                      ) : !searchResults || searchResults.length === 0 ? (
                        <div className="text-center py-12">
                          <div className="max-w-sm mx-auto">
                            <Search className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                            <h3 className="font-semibold text-gray-600 dark:text-gray-400 mb-2">
                              Nenhum medicamento encontrado
                            </h3>
                            <p className="text-sm text-gray-500 dark:text-gray-500">
                              Tente buscar por outro termo ou verifique a ortografia
                            </p>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-6">
                          <div className="flex items-center justify-between">
                            <p className="text-blue-700 dark:text-blue-300 font-medium">
                              {searchResults.length} medicamento(s) encontrado(s)
                            </p>
                            <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                              Pesquisa: "{searchQuery}"
                            </Badge>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {searchResults.map((result) => {
                              // Debug: verificar estrutura dos dados após processamento
                              console.log('Resultado processado:', result);

                              // Normalizar dados da busca para interface Medication
                              const medication: Medication = {
                                id: result.id,
                                name: result.name,
                                compatibility_level: result.compatibility_level || 'Não definido',
                                usage_description: result.usage_description || '',
                                additional_info: result.additional_info,
                                efeitos_no_lactente: result.efeitos_no_lactente,
                                alternativas_seguras: result.alternativas_seguras,
                                orientacoes_uso: result.orientacoes_uso,
                                section_name: result.section_name,
                                subsection_name: result.subsection_name
                              };

                              return (
                                <div key={medication.id}>
                                  {renderMedication(medication)}
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Breadcrumbs */}
              {navigation.breadcrumbs.length > 0 && (
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-6">
                  <button
                    className="hover:text-pink-600 dark:hover:text-pink-400 transition-colors flex items-center gap-2"
                    onClick={() => setNavigation({
                      currentLevel: 'sections',
                      currentSectionId: null,
                      currentSubsectionId: null,
                      breadcrumbs: []
                    })}
                  >
                    <Home className="h-4 w-4" />
                    Início
                  </button>
                  {navigation.breadcrumbs.map((crumb, index) => (
                    <React.Fragment key={crumb.id}>
                      <ChevronRight className="h-3 w-3" />
                      <button
                        className="hover:text-pink-600 dark:hover:text-pink-400 transition-colors"
                        onClick={() => {
                          if (crumb.type === 'section') {
                            setNavigation({
                              currentLevel: 'subsections',
                              currentSectionId: crumb.id,
                              currentSubsectionId: null,
                              breadcrumbs: navigation.breadcrumbs.slice(0, index + 1)
                            });
                          }
                        }}
                      >
                        {crumb.name}
                      </button>
                    </React.Fragment>
                  ))}
                </div>
              )}

              {/* Conteúdo Principal */}
              {navigation.currentLevel === 'sections' && (
                <div className="space-y-6">
                  <div className="text-center mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                      Categorias de Medicamentos
                    </h2>
                    <p className="text-gray-600 dark:text-gray-400">
                      Selecione uma categoria para explorar os medicamentos
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {breastfeedingData.sections
                      .filter(section => sectionHasVisibleMedications(section))
                      .sort((a, b) => {
                        // Calcular total de medicamentos para seção A
                        const totalA = (a.medications?.length || 0) +
                          (a.subsections?.reduce((acc, sub) => acc + (sub.medications?.length || 0), 0) || 0);

                        // Calcular total de medicamentos para seção B
                        const totalB = (b.medications?.length || 0) +
                          (b.subsections?.reduce((acc, sub) => acc + (sub.medications?.length || 0), 0) || 0);

                        // Ordenar do maior para o menor
                        return totalB - totalA;
                      })
                      .map(section => renderSectionCard(section))}
                  </div>

                  {/* Mensagem quando nenhuma seção é encontrada com filtro */}
                  {activeFilter && breastfeedingData.sections.filter(section => sectionHasVisibleMedications(section)).length === 0 && (
                    <div className="text-center py-12">
                      <Card className="max-w-md mx-auto border-gray-200 dark:border-gray-700">
                        <CardContent className="p-8">
                          <Filter className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                          <h3 className="font-semibold text-gray-600 dark:text-gray-400 mb-2">
                            Nenhuma categoria encontrada
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-500 mb-4">
                            Nenhuma medicação encontrada com o filtro selecionado.
                          </p>
                          <Button
                            variant="outline"
                            onClick={() => setActiveFilter(null)}
                          >
                            Limpar filtro
                          </Button>
                        </CardContent>
                      </Card>
                    </div>
                  )}
                </div>
              )}

              {navigation.currentLevel === 'subsections' && navigation.currentSectionId && (() => {
                const currentSection = breastfeedingData.sections.find(s => s.id === navigation.currentSectionId);
                if (!currentSection) return null;

                return (
                  <div className="space-y-6">
                    <div className="text-center mb-8">
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                        {currentSection.name}
                      </h2>
                      {currentSection.description && (
                        <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                          {currentSection.description}
                        </p>
                      )}
                    </div>

                    {/* Medicamentos diretos da seção */}
                    {currentSection.medications && currentSection.medications.length > 0 && (
                      <div className="mb-8">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center gap-2">
                          <Pill className="h-5 w-5" />
                          Medicamentos Gerais
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {currentSection.medications
                            .filter(med => medicationMatchesFilter(med))
                            .map(medication => renderMedication(medication))}
                        </div>
                      </div>
                    )}

                    {/* Subcategorias */}
                    {currentSection.subsections && currentSection.subsections.length > 0 && (
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center gap-2">
                          <FolderOpen className="h-5 w-5" />
                          Subcategorias
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {currentSection.subsections
                            .filter(sub => subsectionHasVisibleMedications(sub))
                            .sort((a, b) => {
                              // Calcular total de medicamentos para subsection A
                              const totalA = (a.medications?.length || 0) +
                                (a.subsections?.reduce((acc, sub) => acc + (sub.medications?.length || 0), 0) || 0);

                              // Calcular total de medicamentos para subsection B
                              const totalB = (b.medications?.length || 0) +
                                (b.subsections?.reduce((acc, sub) => acc + (sub.medications?.length || 0), 0) || 0);

                              // Ordenar do maior para o menor
                              return totalB - totalA;
                            })
                            .map(subsection => (
                              <Card
                                key={subsection.id}
                                className="group cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 border-2 border-blue-200 dark:border-blue-700 hover:border-blue-300 dark:hover:border-blue-600 rounded-xl overflow-hidden bg-gradient-to-br from-blue-50/50 to-white dark:from-blue-950/20 dark:to-gray-800"
                                onClick={() => navigateToSubsection(currentSection, subsection)}
                              >
                                <CardContent className="p-4">
                                  <div className="flex items-start justify-between mb-3">
                                    <div className="flex items-center gap-3">
                                      <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg group-hover:bg-blue-200 dark:group-hover:bg-blue-800/40 transition-colors">
                                        <FolderOpen className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                                      </div>
                                      <div>
                                        <h4 className="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                                          {subsection.name}
                                        </h4>
                                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                          {subsection.medications?.filter(med => medicationMatchesFilter(med)).length || 0} medicamento(s)
                                        </p>
                                      </div>
                                    </div>
                                    <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-blue-500 transition-colors" />
                                  </div>

                                  {subsection.description && (
                                    <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                                      {subsection.description}
                                    </p>
                                  )}
                                </CardContent>
                              </Card>
                            ))}
                        </div>
                      </div>
                    )}

                    {/* Mensagem quando não há conteúdo visível */}
                    {(!currentSection.medications?.some(med => medicationMatchesFilter(med)) &&
                      !currentSection.subsections?.some(sub => subsectionHasVisibleMedications(sub))) && (
                      <div className="text-center py-12">
                        <Card className="max-w-md mx-auto border-gray-200 dark:border-gray-700">
                          <CardContent className="p-8">
                            <Filter className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                            <h3 className="font-semibold text-gray-600 dark:text-gray-400 mb-2">
                              Nenhum medicamento encontrado
                            </h3>
                            <p className="text-sm text-gray-500 dark:text-gray-500 mb-4">
                              Nenhum medicamento encontrado nesta categoria com o filtro atual.
                            </p>
                            <Button
                              variant="outline"
                              onClick={() => setActiveFilter(null)}
                            >
                              Limpar filtro
                            </Button>
                          </CardContent>
                        </Card>
                      </div>
                    )}
                  </div>
                );
              })()}

              {navigation.currentLevel === 'medications' && navigation.currentSectionId && navigation.currentSubsectionId && (() => {
                const currentSection = breastfeedingData.sections.find(s => s.id === navigation.currentSectionId);
                const currentSubsection = currentSection?.subsections?.find(sub => sub.id === navigation.currentSubsectionId);

                if (!currentSection || !currentSubsection) return null;

                return (
                  <div className="space-y-6">
                    <div className="text-center mb-8">
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                        {currentSubsection.name}
                      </h2>
                      {currentSubsection.description && (
                        <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                          {currentSubsection.description}
                        </p>
                      )}
                    </div>

                    {currentSubsection.medications && currentSubsection.medications.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {currentSubsection.medications
                          .filter(med => medicationMatchesFilter(med))
                          .sort((a, b) => a.name.localeCompare(b.name, 'pt-BR'))
                          .map(medication => renderMedication(medication))}
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <Card className="max-w-md mx-auto border-gray-200 dark:border-gray-700">
                          <CardContent className="p-8">
                            <Pill className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                            <h3 className="font-semibold text-gray-600 dark:text-gray-400 mb-2">
                              Nenhum medicamento disponível
                            </h3>
                            <p className="text-sm text-gray-500 dark:text-gray-500">
                              Esta subcategoria ainda não possui medicamentos cadastrados.
                            </p>
                          </CardContent>
                        </Card>
                      </div>
                    )}

                    {/* Mensagem quando filtro não retorna resultados */}
                    {currentSubsection.medications &&
                     currentSubsection.medications.length > 0 &&
                     !currentSubsection.medications.some(med => medicationMatchesFilter(med)) && (
                      <div className="text-center py-12">
                        <Card className="max-w-md mx-auto border-gray-200 dark:border-gray-700">
                          <CardContent className="p-8">
                            <Filter className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                            <h3 className="font-semibold text-gray-600 dark:text-gray-400 mb-2">
                              Nenhum medicamento encontrado
                            </h3>
                            <p className="text-sm text-gray-500 dark:text-gray-500 mb-4">
                              Nenhum medicamento encontrado nesta subcategoria com o filtro atual.
                            </p>
                            <Button
                              variant="outline"
                              onClick={() => setActiveFilter(null)}
                            >
                              Limpar filtro
                            </Button>
                          </CardContent>
                        </Card>
                      </div>
                    )}
                  </div>
                );
              })()}
            </>
          )}

          {/* Disclaimer e Fontes */}
          <div className="mt-12 space-y-6">
            {/* Aviso Importante */}
            <div className="bg-gradient-to-r from-amber-50 via-amber-50/80 to-amber-50 dark:from-amber-900/20 dark:via-amber-900/10 dark:to-amber-900/20 p-6 rounded-xl border border-amber-100 dark:border-amber-800/30 text-center shadow-sm">
              <div className="flex flex-col items-center gap-3">
                <div className="bg-white dark:bg-gray-800 p-2 rounded-full shadow-sm">
                  <AlertTriangle className="h-5 w-5 text-amber-500" />
                </div>
                <div className="space-y-2">
                  <p className="text-base font-medium text-gray-700 dark:text-gray-200">
                    ⚠️ Esta ferramenta é apenas um guia e não substitui a avaliação clínica individual
                  </p>
                  <p className="text-sm text-amber-700 dark:text-amber-300">
                    Sempre consulte um profissional de saúde antes de tomar qualquer decisão sobre medicamentos durante a amamentação.
                  </p>
                </div>
              </div>
            </div>

            {/* Fontes e Referências */}
            <div className="bg-gradient-to-r from-blue-50 via-blue-50/80 to-blue-50 dark:from-blue-900/20 dark:via-blue-900/10 dark:to-blue-900/20 p-6 rounded-xl border border-blue-100 dark:border-blue-800/30 shadow-sm">
              <div className="flex flex-col items-center gap-4">
                <div className="bg-white dark:bg-gray-800 p-2 rounded-full shadow-sm">
                  <Info className="h-5 w-5 text-blue-500" />
                </div>
                <div className="space-y-3 text-center">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                    📚 Fontes e Referências
                  </h3>
                  <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400 max-w-4xl">
                    <p className="font-medium text-blue-700 dark:text-blue-300">
                      <strong>Fonte Principal:</strong> Ministério da Saúde do Brasil
                    </p>
                    <div className="space-y-2 text-left max-w-2xl mx-auto">
                      <p>• <strong>ANVISA:</strong> Agência Nacional de Vigilância Sanitária</p>
                      <p>• <strong>LactMed (NIH):</strong> Base de dados sobre medicamentos e lactação</p>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-500 mt-3 italic">
                      Dados compilados e adaptados para facilitar o acesso a informações baseadas em evidências científicas atualizadas.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default MedicationsBreastfeeding;
