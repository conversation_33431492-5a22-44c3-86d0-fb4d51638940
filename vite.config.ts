
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react(),
    mode === 'development' && componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    ssrManifest: true,
    modulePreload: {
      polyfill: true,
    },
    // Otimizações de performance
    target: 'es2020',
    cssCodeSplit: true,
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          // Core React
          'react-vendor': ['react', 'react-dom'],
          'router-vendor': ['react-router-dom'],

          // UI Libraries - Split into smaller chunks
          'radix-core': [
            '@radix-ui/react-dialog',
            '@radix-ui/react-toast',
            '@radix-ui/react-dropdown-menu',
            '@radix-ui/react-popover'
          ],
          'radix-forms': [
            '@radix-ui/react-select',
            '@radix-ui/react-checkbox',
            '@radix-ui/react-radio-group',
            '@radix-ui/react-label',
            '@radix-ui/react-switch'
          ],
          'radix-layout': [
            '@radix-ui/react-accordion',
            '@radix-ui/react-tabs',
            '@radix-ui/react-collapsible',
            '@radix-ui/react-separator',
            '@radix-ui/react-scroll-area'
          ],
          'radix-navigation': [
            '@radix-ui/react-navigation-menu',
            '@radix-ui/react-menubar',
            '@radix-ui/react-context-menu'
          ],
          'radix-feedback': [
            '@radix-ui/react-alert-dialog',
            '@radix-ui/react-hover-card',
            '@radix-ui/react-tooltip',
            '@radix-ui/react-progress'
          ],

          // Backend & State
          'supabase-vendor': ['@supabase/supabase-js', '@supabase/auth-helpers-react', '@supabase/auth-ui-react'],
          'query-vendor': ['@tanstack/react-query'],
          'state-vendor': ['zustand'],

          // Editors & Rich Content
          'editor-vendor': [
            '@tiptap/react',
            '@tiptap/starter-kit',
            '@tiptap/extension-color',
            '@tiptap/extension-image',
            '@tiptap/extension-link',
            '@tiptap/extension-text-style',
            '@tiptap/extension-underline'
          ],

          // Charts & Visualization - Separados para melhor tree-shaking
          'chart-js': ['chart.js'],
          'recharts': ['recharts'],
          'motion-vendor': ['framer-motion'],

          // Utilities
          'date-vendor': ['date-fns'],
          'form-vendor': ['react-hook-form', '@hookform/resolvers', 'zod'],
          'dnd-vendor': ['@dnd-kit/core', '@dnd-kit/sortable'],
          'markdown-vendor': ['react-markdown', 'remark-math', 'rehype-katex'],

          // Heavy Libraries
          'pdf-vendor': ['jspdf'],
          'carousel-vendor': ['embla-carousel-react'],

          // 3D/Cinematic Components - Lazy loaded apenas quando necessário
          'cinematic-vendor': ['@/components/3d/CinematicWelcome'],
        },
      },
    },
    // Otimizar o tamanho do bundle
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
        passes: 2,
      },
      mangle: {
        safari10: true,
      },
    },
    // Configurações adicionais de otimização
    chunkSizeWarningLimit: 1000,
    assetsInlineLimit: 4096,
  },
  ssr: {
    noExternal: ['react-helmet-async'],
    target: 'node',
    format: 'esm'
  }
}));
