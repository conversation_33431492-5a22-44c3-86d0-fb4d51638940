/**
 * Script para gerar páginas SEO das NOVAS seções implementadas
 * (Calculadoras, Puericultura, Intoxicações, Fluxogramas, CID-10, Bulas Profissionais)
 */

import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://bxedpdmgvgatjdfxgxij.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ4ZWRwZG1ndmdhdGpkZnhneGlqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzIyNzk3MTgsImV4cCI6MjA0Nzg1NTcxOH0.cjoaggOXt1kY9WmVNbAipCOQ2dP4PWLP43KMf8cO8Wo';
const supabase = createClient(supabaseUrl, supabaseKey);

console.log('🚀 Gerando páginas SEO das NOVAS seções...');

/**
 * Dados das calculadoras
 */
const calculatorsData = [
  {
    slug: 'imc',
    name: 'Calculadora de IMC Pediátrico',
    description: 'Calcule o Índice de Massa Corporal (IMC) para crianças e adolescentes com percentis específicos para idade.',
    keywords: 'imc pediatrico, indice massa corporal crianca, percentil imc, obesidade infantil, desnutricao pediatrica'
  },
  {
    slug: 'hidratacao',
    name: 'Calculadora de Hidratação',
    description: 'Calcule necessidades hídricas e reposição de fluidos em pediatria com base no peso e grau de desidratação.',
    keywords: 'hidratacao pediatrica, reposicao fluidos, desidratacao crianca, soro fisiologico, eletroliticos'
  },
  {
    slug: 'superficie-corporal',
    name: 'Calculadora de Superfície Corporal',
    description: 'Calcule a área de superfície corporal (ASC) pediátrica usando fórmulas validadas para dosagem de medicamentos.',
    keywords: 'superficie corporal pediatrica, asc crianca, area superficie corporal, dosagem medicamento'
  },
  {
    slug: 'apgar',
    name: 'Calculadora de Apgar',
    description: 'Avalie o estado do recém-nascido através do escore de Apgar nos primeiros minutos de vida.',
    keywords: 'apgar score, escore apgar, avaliacao recem nascido, vitalidade neonatal'
  },
  {
    slug: 'glasgow',
    name: 'Escala de Glasgow Pediátrica',
    description: 'Avalie o nível de consciência em crianças usando a Escala de Coma de Glasgow adaptada para pediatria.',
    keywords: 'glasgow pediatrico, escala coma glasgow, nivel consciencia crianca, trauma craniano'
  },
  {
    slug: 'capurro',
    name: 'Método de Capurro',
    description: 'Determine a idade gestacional do recém-nascido através do método de Capurro com avaliação somática.',
    keywords: 'capurro metodo, idade gestacional, avaliacao somatica, recem nascido prematuro'
  },
  {
    slug: 'ballard',
    name: 'Método de Ballard',
    description: 'Avalie a maturidade do recém-nascido através do método de Ballard com critérios neurológicos e físicos.',
    keywords: 'ballard score, maturidade neonatal, idade gestacional ballard, avaliacao neurologica'
  },
  {
    slug: 'finnegan',
    name: 'Escala de Finnegan',
    description: 'Avalie síndrome de abstinência neonatal através da escala de Finnegan para recém-nascidos.',
    keywords: 'finnegan escala, sindrome abstinencia neonatal, abstinencia drogas recem nascido'
  },
  {
    slug: 'bhutani',
    name: 'Nomograma de Bhutani',
    description: 'Avalie o risco de hiperbilirrubinemia em recém-nascidos através do nomograma de Bhutani.',
    keywords: 'bhutani nomograma, hiperbilirrubinemia, ictericia neonatal, bilirrubina recem nascido'
  },
  {
    slug: 'rodwell',
    name: 'Calculadora de Rodwell',
    description: 'Avalie parâmetros hematológicos em recém-nascidos através dos valores de referência de Rodwell.',
    keywords: 'rodwell valores, hematologia neonatal, hemograma recem nascido, valores referencia'
  },
  {
    slug: 'gina',
    name: 'Calculadora GINA',
    description: 'Avalie e classifique a asma pediátrica seguindo as diretrizes GINA (Global Initiative for Asthma).',
    keywords: 'gina asma, classificacao asma pediatrica, controle asma crianca, diretrizes gina'
  }
];

/**
 * Dados da puericultura
 */
const puericultureData = [
  {
    slug: 'puericultura',
    name: 'Puericultura',
    description: 'Acompanhamento completo do desenvolvimento infantil com ferramentas para consultas de puericultura.',
    keywords: 'puericultura, acompanhamento infantil, desenvolvimento crianca, consulta pediatrica'
  },
  {
    slug: 'puericultura/curva-de-crescimento',
    name: 'Curvas de Crescimento',
    description: 'Monitore o crescimento infantil com curvas de peso, altura e perímetro cefálico da OMS.',
    keywords: 'curva crescimento, percentil peso altura, oms crescimento, desenvolvimento fisico'
  },
  {
    slug: 'puericultura/calendario-vacinal',
    name: 'Calendário Vacinal',
    description: 'Calendário de vacinação infantil atualizado com todas as vacinas recomendadas pelo Ministério da Saúde.',
    keywords: 'calendario vacinal, vacinas crianca, imunizacao pediatrica, esquema vacinal'
  },
  {
    slug: 'puericultura/formulas',
    name: 'Fórmulas Infantis',
    description: 'Guia completo sobre fórmulas infantis, preparo e indicações para alimentação de lactentes.',
    keywords: 'formula infantil, leite artificial, alimentacao lactente, preparo mamadeira'
  },
  {
    slug: 'puericultura/suplementacao-infantil',
    name: 'Suplementação Infantil',
    description: 'Orientações sobre suplementação vitamínica e mineral em crianças conforme faixa etária.',
    keywords: 'suplementacao infantil, vitaminas crianca, ferro pediatrico, vitamina d'
  },
  {
    slug: 'puericultura/patient-overview',
    name: 'Visão Geral do Paciente',
    description: 'Ferramenta para avaliação global do paciente pediátrico com dados antropométricos e desenvolvimento.',
    keywords: 'avaliacao pediatrica, dados antropometricos, desenvolvimento neuropsicomotor'
  },
  {
    slug: 'dnpm',
    name: 'DNPM - Desenvolvimento Neuropsicomotor',
    description: 'Avalie marcos do desenvolvimento neuropsicomotor em crianças por faixa etária.',
    keywords: 'dnpm, desenvolvimento neuropsicomotor, marcos desenvolvimento, atraso desenvolvimento'
  }
];

/**
 * Dados das intoxicações
 */
const poisoningsData = [
  {
    slug: 'poisonings',
    name: 'Intoxicações Pediátricas',
    description: 'Guia completo para manejo de intoxicações em pediatria com antídotos e doses específicas.',
    keywords: 'intoxicacao pediatrica, envenenamento crianca, antidotos pediatricos, emergencia toxicologica'
  },
  {
    slug: 'poisonings/benzodiazepinicos',
    name: 'Intoxicação por Benzodiazepínicos',
    description: 'Manejo da intoxicação por benzodiazepínicos em crianças: sintomas, antídoto flumazenil e doses.',
    keywords: 'benzodiazepinicos intoxicacao, flumazenil pediatrico, overdose benzodiazepinicos'
  },
  {
    slug: 'poisonings/opioides',
    name: 'Intoxicação por Opioides',
    description: 'Tratamento da intoxicação por opioides em pediatria: naloxona, doses e manejo da depressão respiratória.',
    keywords: 'opioides intoxicacao, naloxona pediatrica, overdose opioides, depressao respiratoria'
  },
  {
    slug: 'poisonings/anticolinergicos',
    name: 'Intoxicação por Anticolinérgicos',
    description: 'Manejo da intoxicação por anticolinérgicos em crianças: sintomas, fisostigmina e cuidados específicos.',
    keywords: 'anticolinergicos intoxicacao, fisostigmina pediatrica, atropina overdose'
  },
  {
    slug: 'poisonings/simpatomimeticos',
    name: 'Intoxicação por Simpatomiméticos',
    description: 'Tratamento da intoxicação por simpatomiméticos: anfetaminas, cocaína e manejo cardiovascular.',
    keywords: 'simpatomimeticos intoxicacao, anfetaminas pediatrica, cocaina crianca'
  },
  {
    slug: 'poisonings/colinergicos',
    name: 'Intoxicação por Colinérgicos',
    description: 'Manejo da intoxicação por organofosforados e carbamatos: atropina, pralidoxima e descontaminação.',
    keywords: 'colinergicos intoxicacao, organofosforados, atropina pralidoxima'
  },
  {
    slug: 'poisonings/metemoglobinemia',
    name: 'Metemoglobinemia',
    description: 'Diagnóstico e tratamento da metemoglobinemia em pediatria: azul de metileno e causas.',
    keywords: 'metemoglobinemia pediatrica, azul metileno, cianose central'
  },
  {
    slug: 'poisonings/paracetamol',
    name: 'Intoxicação por Paracetamol',
    description: 'Manejo da intoxicação por paracetamol: N-acetilcisteína, nomograma e hepatotoxicidade.',
    keywords: 'paracetamol intoxicacao, acetaminofeno overdose, n-acetilcisteina'
  },
  {
    slug: 'poisonings/antidepressivos_triciclicos',
    name: 'Intoxicação por Antidepressivos Tricíclicos',
    description: 'Tratamento da intoxicação por tricíclicos: bicarbonato de sódio, arritmias e convulsões.',
    keywords: 'triciclicos intoxicacao, bicarbonato sodio, arritmias pediatricas'
  },
  {
    slug: 'poisonings/betabloqueadores',
    name: 'Intoxicação por Betabloqueadores',
    description: 'Manejo da intoxicação por betabloqueadores: glucagon, atropina e suporte cardiovascular.',
    keywords: 'betabloqueadores intoxicacao, glucagon pediatrico, bradicardia'
  }
];

/**
 * Dados dos fluxogramas
 */
const flowchartsData = [
  {
    slug: 'fluxogramas/asma',
    name: 'Fluxograma de Asma',
    description: 'Fluxograma para diagnóstico e manejo da asma pediátrica com classificação de gravidade.',
    keywords: 'asma pediatrica, fluxograma asma, broncoespasmo crianca, tratamento asma'
  },
  {
    slug: 'fluxogramas/anafilaxia',
    name: 'Fluxograma de Anafilaxia',
    description: 'Protocolo de emergência para anafilaxia pediátrica: reconhecimento e tratamento imediato.',
    keywords: 'anafilaxia pediatrica, choque anafilatico, epinefrina crianca, alergia grave'
  },
  {
    slug: 'fluxogramas/convulsao',
    name: 'Fluxograma de Convulsão',
    description: 'Manejo da convulsão pediátrica: status epilepticus, medicações e protocolo de emergência.',
    keywords: 'convulsao pediatrica, status epilepticus, anticonvulsivantes, emergencia neurologica'
  },
  {
    slug: 'fluxogramas/cetoacidose',
    name: 'Fluxograma de Cetoacidose',
    description: 'Protocolo para cetoacidose diabética pediátrica: hidratação, insulina e correção eletrolítica.',
    keywords: 'cetoacidose diabetica, diabetes pediatrico, insulina crianca, acidose metabolica'
  },
  {
    slug: 'fluxogramas/dengue',
    name: 'Fluxograma de Dengue',
    description: 'Manejo da dengue pediátrica: classificação, sinais de alarme e tratamento.',
    keywords: 'dengue pediatrica, febre hemorragica, choque dengue, sinais alarme'
  },
  {
    slug: 'fluxogramas/pecarn',
    name: 'Fluxograma PECARN',
    description: 'Regra PECARN para trauma craniano pediátrico: indicações para tomografia computadorizada.',
    keywords: 'pecarn regra, trauma craniano pediatrico, tomografia crianca, traumatismo cranio'
  },
  {
    slug: 'fluxogramas/animais-peconhentos/botrópico',
    name: 'Acidente Botrópico',
    description: 'Manejo do acidente botrópico em crianças: soro antibotrópico, doses e complicações.',
    keywords: 'acidente botropico, jararaca picada, soro antibotropico, envenenamento ofidico'
  },
  {
    slug: 'fluxogramas/animais-peconhentos/crotálico',
    name: 'Acidente Crotálico',
    description: 'Tratamento do acidente crotálico pediátrico: soro anticrotálico e manifestações clínicas.',
    keywords: 'acidente crotalico, cascavel picada, soro anticrotalico, miotoxicidade'
  },
  {
    slug: 'fluxogramas/animais-peconhentos/elapídico',
    name: 'Acidente Elapídico',
    description: 'Manejo do acidente elapídico: coral verdadeira, soro antielapídico e paralisia.',
    keywords: 'acidente elapidico, coral picada, soro antielapidico, paralisia flacida'
  },
  {
    slug: 'fluxogramas/animais-peconhentos/loxoscélico',
    name: 'Acidente Loxoscélico',
    description: 'Tratamento do loxoscelismo: aranha marrom, necrose cutânea e soro antiloxoscélico.',
    keywords: 'loxoscelismo, aranha marrom, necrose cutanea, soro antiloxoscelico'
  },
  {
    slug: 'fluxogramas/animais-peconhentos/phoneutria',
    name: 'Acidente por Phoneutria',
    description: 'Manejo do acidente por Phoneutria: aranha armadeira, dor local e soro antiaracnídico.',
    keywords: 'phoneutria picada, aranha armadeira, soro antiaracnidico, dor neuropatica'
  },
  {
    slug: 'fluxogramas/animais-peconhentos/escorpiao',
    name: 'Acidente Escorpiônico',
    description: 'Tratamento do acidente escorpiônico pediátrico: soro antiescorpiônico e manifestações.',
    keywords: 'acidente escorpionico, escorpiao picada, soro antiescorpionico, dor local'
  }
];

/**
 * Dados do CID-10
 */
const icdData = [
  {
    slug: 'icd',
    name: 'CID-10 Pediátrico',
    description: 'Classificação Internacional de Doenças (CID-10) com códigos específicos para pediatria.',
    keywords: 'cid 10 pediatrico, classificacao doencas, codigos cid pediatria, diagnosticos pediatricos'
  }
];

/**
 * Dados dos medicamentos na amamentação
 */
const breastfeedingMedicationsData = [
  {
    slug: 'medicamentos-amamentacao',
    name: 'Medicamentos na Amamentação',
    description: 'Guia completo sobre segurança de medicamentos durante a amamentação com classificação de compatibilidade e orientações específicas.',
    keywords: 'medicamentos amamentacao, lactacao medicamentos, compatibilidade amamentacao, seguranca medicamentos lactacao, drogas amamentacao'
  }
];

/**
 * Buscar medicamentos na amamentação do Supabase
 */
async function fetchBreastfeedingMedications() {
  console.log('🤱 Buscando medicamentos na amamentação do Supabase...');

  try {
    const { data: medications, error } = await supabase.rpc('get_breastfeeding_structure_cached');

    if (error) {
      console.error('❌ Erro ao buscar medicamentos na amamentação:', error);
      return [];
    }

    if (medications && medications.sections) {
      console.log(`✅ Encontradas ${medications.sections.length} seções de medicamentos na amamentação`);

      // Extrair medicamentos individuais para páginas específicas
      const allMedications = [];
      medications.sections.forEach(section => {
        if (section.medications) {
          section.medications.forEach(med => {
            allMedications.push({
              id: med.id,
              name: med.name,
              slug: med.name.toLowerCase()
                .replace(/[^a-z0-9\s]/g, '')
                .replace(/\s+/g, '-')
                .trim(),
              compatibility_level: med.compatibility_level,
              usage_description: med.usage_description,
              section_name: section.name
            });
          });
        }

        if (section.subsections) {
          section.subsections.forEach(subsection => {
            if (subsection.medications) {
              subsection.medications.forEach(med => {
                allMedications.push({
                  id: med.id,
                  name: med.name,
                  slug: med.name.toLowerCase()
                    .replace(/[^a-z0-9\s]/g, '')
                    .replace(/\s+/g, '-')
                    .trim(),
                  compatibility_level: med.compatibility_level,
                  usage_description: med.usage_description,
                  section_name: section.name,
                  subsection_name: subsection.name
                });
              });
            }
          });
        }
      });

      console.log(`✅ Total de ${allMedications.length} medicamentos individuais encontrados`);
      return allMedications;
    } else {
      console.log('⚠️ Nenhum medicamento na amamentação encontrado');
      return [];
    }

  } catch (error) {
    console.error('❌ Erro na conexão com Supabase para medicamentos na amamentação:', error);
    return [];
  }
}

/**
 * Buscar bulas profissionais do Supabase
 */
async function fetchProfessionalLabels() {
  console.log('📋 Buscando bulas profissionais do Supabase...');

  try {
    const { data: labels, error } = await supabase
      .from('pedbook_medication_instructions')
      .select(`
        id,
        content,
        pedbook_medications (
          name,
          slug
        )
      `)
      .eq('is_published', true);

    if (error) {
      console.error('❌ Erro ao buscar bulas:', error);
      return [];
    }

    if (labels && labels.length > 0) {
      // Filtrar apenas bulas que têm medicamento associado
      const validLabels = labels.filter(label =>
        label.pedbook_medications &&
        label.pedbook_medications.name &&
        label.pedbook_medications.slug
      );

      console.log(`✅ Encontradas ${validLabels.length} bulas profissionais válidas`);

      // Mapear para formato mais simples
      return validLabels.map(label => ({
        id: label.id,
        title: label.pedbook_medications.name,
        slug: label.pedbook_medications.slug,
        content: label.content
      }));
    } else {
      console.log('⚠️ Nenhuma bula encontrada');
      return [];
    }

  } catch (error) {
    console.error('❌ Erro na conexão com Supabase para bulas:', error);
    return [];
  }
}

/**
 * Gerar páginas HTML para calculadoras
 */
async function generateCalculatorPages() {
  console.log('\n🧮 Gerando páginas de calculadoras...');
  
  const distPath = path.join(process.cwd(), 'dist');
  const indexPath = path.join(distPath, 'index.html');
  
  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html não encontrado');
    return 0;
  }
  
  const baseHtml = fs.readFileSync(indexPath, 'utf8');
  let successCount = 0;
  
  for (const calc of calculatorsData) {
    try {
      const calcDir = path.join(distPath, 'calculadoras', calc.slug);
      if (!fs.existsSync(calcDir)) {
        fs.mkdirSync(calcDir, { recursive: true });
      }
      
      const title = `${calc.name} | PedBook`;
      const description = calc.description;
      const keywords = calc.keywords;
      
      let customHtml = baseHtml;
      
      // Substituir meta tags
      customHtml = customHtml.replace(/<title>.*?<\/title>/, `<title>${title}</title>`);
      customHtml = customHtml.replace(/<meta name="description" content=".*?" ?\/>/, `<meta name="description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta name="keywords" content=".*?" ?\/>/, `<meta name="keywords" content="${keywords}" />`);
      
      // URL canônica
      const calcUrl = `https://pedb.com.br/calculadoras/${calc.slug}`;
      customHtml = customHtml.replace(/<link rel="canonical" href=".*?" \/>/, `<link rel="canonical" href="${calcUrl}" />`);
      
      // Open Graph
      customHtml = customHtml.replace(/<meta property="og:title" content=".*?" ?\/>/, `<meta property="og:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta property="og:description" content=".*?" ?\/>/, `<meta property="og:description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta property="og:url" content=".*?" ?\/>/, `<meta property="og:url" content="${calcUrl}" />`);
      
      // Twitter Cards
      customHtml = customHtml.replace(/<meta name="twitter:title" content=".*?" ?\/>/, `<meta name="twitter:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta name="twitter:description" content=".*?" ?\/>/, `<meta name="twitter:description" content="${description}" />`);
      
      // Conteúdo SEO
      const seoContent = `
    <div id="root"></div>
    <!-- SEO Content for Crawlers -->
    <div style="display: none;" data-seo-content>
      <h1>${calc.name}</h1>
      <p>${calc.description}</p>
      <h2>Calculadora Pediátrica Automática</h2>
      <p>Ferramenta especializada para cálculos em pediatria com resultados precisos e baseados em evidências científicas.</p>
      <h2>Como Usar</h2>
      <p>Insira os dados solicitados e obtenha resultados automáticos com interpretação clínica adequada para pediatria.</p>
    </div>`;
      
      customHtml = customHtml.replace('<div id="root"></div>', seoContent);
      
      // Schema.org
      const schema = {
        "@context": "https://schema.org",
        "@type": "MedicalWebPage",
        "name": calc.name,
        "description": description,
        "url": calcUrl,
        "medicalAudience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde Pediátrica"
        },
        "about": {
          "@type": "MedicalProcedure",
          "name": calc.name,
          "medicalSpecialty": "Pediatria"
        },
        "publisher": {
          "@type": "Organization",
          "name": "PedBook",
          "url": "https://pedb.com.br"
        }
      };
      
      const jsonLd = `<script type="application/ld+json">${JSON.stringify(schema, null, 2)}</script>`;
      customHtml = customHtml.replace('</head>', `  ${jsonLd}\n</head>`);
      
      fs.writeFileSync(path.join(calcDir, 'index.html'), customHtml);
      console.log(`✅ Criado: /calculadoras/${calc.slug}/index.html`);
      successCount++;
      
    } catch (err) {
      console.error(`❌ Erro ao gerar ${calc.slug}:`, err.message);
    }
  }
  
  console.log(`🧮 Calculadoras: ${successCount}/${calculatorsData.length} páginas geradas`);
  return successCount;
}

/**
 * Gerar páginas HTML para puericultura
 */
async function generatePuericulturePages() {
  console.log('\n👶 Gerando páginas de puericultura...');

  const distPath = path.join(process.cwd(), 'dist');
  const indexPath = path.join(distPath, 'index.html');

  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html não encontrado');
    return 0;
  }

  const baseHtml = fs.readFileSync(indexPath, 'utf8');
  let successCount = 0;

  for (const page of puericultureData) {
    try {
      const pageDir = path.join(distPath, page.slug);
      if (!fs.existsSync(pageDir)) {
        fs.mkdirSync(pageDir, { recursive: true });
      }

      const title = `${page.name} - Puericultura | PedBook`;
      const description = page.description;
      const keywords = page.keywords;

      let customHtml = baseHtml;

      // Substituir meta tags
      customHtml = customHtml.replace(/<title>.*?<\/title>/, `<title>${title}</title>`);
      customHtml = customHtml.replace(/<meta name="description" content=".*?" ?\/>/, `<meta name="description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta name="keywords" content=".*?" ?\/>/, `<meta name="keywords" content="${keywords}" />`);

      // URL canônica
      const pageUrl = `https://pedb.com.br/${page.slug}`;
      customHtml = customHtml.replace(/<link rel="canonical" href=".*?" \/>/, `<link rel="canonical" href="${pageUrl}" />`);

      // Open Graph
      customHtml = customHtml.replace(/<meta property="og:title" content=".*?" ?\/>/, `<meta property="og:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta property="og:description" content=".*?" ?\/>/, `<meta property="og:description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta property="og:url" content=".*?" ?\/>/, `<meta property="og:url" content="${pageUrl}" />`);

      // Twitter Cards
      customHtml = customHtml.replace(/<meta name="twitter:title" content=".*?" ?\/>/, `<meta name="twitter:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta name="twitter:description" content=".*?" ?\/>/, `<meta name="twitter:description" content="${description}" />`);

      // Conteúdo SEO
      const seoContent = `
    <div id="root"></div>
    <!-- SEO Content for Crawlers -->
    <div style="display: none;" data-seo-content>
      <h1>${page.name}</h1>
      <p>${page.description}</p>
      <h2>Acompanhamento Pediátrico</h2>
      <p>Ferramenta especializada para acompanhamento do desenvolvimento infantil e consultas de puericultura.</p>
      <h2>Desenvolvimento Infantil</h2>
      <p>Monitore marcos do desenvolvimento, crescimento e saúde da criança de forma sistemática.</p>
    </div>`;

      customHtml = customHtml.replace('<div id="root"></div>', seoContent);

      // Schema.org
      const schema = {
        "@context": "https://schema.org",
        "@type": "MedicalWebPage",
        "name": page.name,
        "description": description,
        "url": pageUrl,
        "medicalAudience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde Pediátrica"
        },
        "about": {
          "@type": "MedicalCondition",
          "name": page.name,
          "medicalSpecialty": "Pediatria"
        },
        "publisher": {
          "@type": "Organization",
          "name": "PedBook",
          "url": "https://pedb.com.br"
        }
      };

      const jsonLd = `<script type="application/ld+json">${JSON.stringify(schema, null, 2)}</script>`;
      customHtml = customHtml.replace('</head>', `  ${jsonLd}\n</head>`);

      fs.writeFileSync(path.join(pageDir, 'index.html'), customHtml);
      console.log(`✅ Criado: /${page.slug}/index.html`);
      successCount++;

    } catch (err) {
      console.error(`❌ Erro ao gerar ${page.slug}:`, err.message);
    }
  }

  console.log(`👶 Puericultura: ${successCount}/${puericultureData.length} páginas geradas`);
  return successCount;
}

/**
 * Gerar páginas HTML para medicamentos na amamentação
 */
async function generateBreastfeedingMedicationPages() {
  console.log('\n🤱 Gerando páginas de medicamentos na amamentação...');

  const distPath = path.join(process.cwd(), 'dist');
  const indexPath = path.join(distPath, 'index.html');

  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html não encontrado');
    return 0;
  }

  const baseHtml = fs.readFileSync(indexPath, 'utf8');
  let successCount = 0;

  // Gerar página principal
  for (const page of breastfeedingMedicationsData) {
    try {
      const pageDir = path.join(distPath, page.slug);
      if (!fs.existsSync(pageDir)) {
        fs.mkdirSync(pageDir, { recursive: true });
      }

      const title = `${page.name} | PedBook`;
      const description = page.description;
      const keywords = page.keywords;

      let customHtml = baseHtml;

      // Substituir meta tags
      customHtml = customHtml.replace(/<title>.*?<\/title>/, `<title>${title}</title>`);
      customHtml = customHtml.replace(/<meta name="description" content=".*?" ?\/>/, `<meta name="description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta name="keywords" content=".*?" ?\/>/, `<meta name="keywords" content="${keywords}" />`);

      // URL canônica
      const pageUrl = `https://pedb.com.br/${page.slug}`;
      customHtml = customHtml.replace(/<link rel="canonical" href=".*?" \/>/, `<link rel="canonical" href="${pageUrl}" />`);

      // Open Graph
      customHtml = customHtml.replace(/<meta property="og:title" content=".*?" ?\/>/, `<meta property="og:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta property="og:description" content=".*?" ?\/>/, `<meta property="og:description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta property="og:url" content=".*?" ?\/>/, `<meta property="og:url" content="${pageUrl}" />`);

      // Twitter Cards
      customHtml = customHtml.replace(/<meta name="twitter:title" content=".*?" ?\/>/, `<meta name="twitter:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta name="twitter:description" content=".*?" ?\/>/, `<meta name="twitter:description" content="${description}" />`);

      // Conteúdo SEO
      const seoContent = `
    <div id="root"></div>
    <!-- SEO Content for Crawlers -->
    <div style="display: none;" data-seo-content>
      <h1>${page.name}</h1>
      <p>${page.description}</p>
      <h2>Segurança na Amamentação</h2>
      <p>Guia completo sobre compatibilidade de medicamentos durante a lactação com classificação de segurança baseada em evidências científicas.</p>
      <h2>Classificação de Compatibilidade</h2>
      <p>Sistema de classificação que avalia o risco de medicamentos para mães que amamentam e seus bebês.</p>
      <h2>Orientações Específicas</h2>
      <p>Recomendações detalhadas sobre uso seguro de medicamentos durante a amamentação com alternativas quando necessário.</p>
    </div>`;

      customHtml = customHtml.replace('<div id="root"></div>', seoContent);

      // Schema.org
      const schema = {
        "@context": "https://schema.org",
        "@type": "MedicalWebPage",
        "name": page.name,
        "description": description,
        "url": pageUrl,
        "medicalAudience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde"
        },
        "about": {
          "@type": "MedicalCondition",
          "name": "Amamentação e Medicamentos",
          "medicalSpecialty": "Pediatria"
        },
        "publisher": {
          "@type": "Organization",
          "name": "PedBook",
          "url": "https://pedb.com.br"
        }
      };

      const jsonLd = `<script type="application/ld+json">${JSON.stringify(schema, null, 2)}</script>`;
      customHtml = customHtml.replace('</head>', `  ${jsonLd}\n</head>`);

      fs.writeFileSync(path.join(pageDir, 'index.html'), customHtml);
      console.log(`✅ Criado: /${page.slug}/index.html`);
      successCount++;

    } catch (err) {
      console.error(`❌ Erro ao gerar ${page.slug}:`, err.message);
    }
  }

  // Buscar medicamentos individuais do Supabase
  const breastfeedingMedications = await fetchBreastfeedingMedications();

  // Gerar páginas para medicamentos individuais
  for (const med of breastfeedingMedications) {
    try {
      const medDir = path.join(distPath, 'medicamentos-amamentacao', med.slug);
      if (!fs.existsSync(medDir)) {
        fs.mkdirSync(medDir, { recursive: true });
      }

      const title = `${med.name} na Amamentação | PedBook`;
      const description = `${med.name} durante a amamentação: compatibilidade, segurança e orientações específicas para lactação. ${med.usage_description || ''}`.substring(0, 160);
      const keywords = `${med.name.toLowerCase()} amamentacao, ${med.name.toLowerCase()} lactacao, compatibilidade ${med.name.toLowerCase()}, seguranca amamentacao`;

      let customHtml = baseHtml;

      // Substituir meta tags
      customHtml = customHtml.replace(/<title>.*?<\/title>/, `<title>${title}</title>`);
      customHtml = customHtml.replace(/<meta name="description" content=".*?" ?\/>/, `<meta name="description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta name="keywords" content=".*?" ?\/>/, `<meta name="keywords" content="${keywords}" />`);

      // URL canônica
      const medUrl = `https://pedb.com.br/medicamentos-amamentacao/${med.slug}`;
      customHtml = customHtml.replace(/<link rel="canonical" href=".*?" \/>/, `<link rel="canonical" href="${medUrl}" />`);

      // Open Graph
      customHtml = customHtml.replace(/<meta property="og:title" content=".*?" ?\/>/, `<meta property="og:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta property="og:description" content=".*?" ?\/>/, `<meta property="og:description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta property="og:url" content=".*?" ?\/>/, `<meta property="og:url" content="${medUrl}" />`);

      // Twitter Cards
      customHtml = customHtml.replace(/<meta name="twitter:title" content=".*?" ?\/>/, `<meta name="twitter:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta name="twitter:description" content=".*?" ?\/>/, `<meta name="twitter:description" content="${description}" />`);

      // Conteúdo SEO específico do medicamento
      const compatibilityText = med.compatibility_level === 'compatible' ? 'compatível' :
                               med.compatibility_level === 'caution' ? 'usar com cautela' :
                               med.compatibility_level === 'avoid' ? 'evitar' : 'consultar especialista';

      const seoContent = `
    <div id="root"></div>
    <!-- SEO Content for Crawlers -->
    <div style="display: none;" data-seo-content>
      <h1>${med.name} na Amamentação</h1>
      <p>Informações sobre o uso de ${med.name} durante a amamentação: ${compatibilityText}.</p>
      <h2>Compatibilidade com Amamentação</h2>
      <p>${med.name} é classificado como ${compatibilityText} durante a lactação.</p>
      <h2>Orientações de Uso</h2>
      <p>${med.usage_description || `Consulte sempre um profissional de saúde antes de usar ${med.name} durante a amamentação.`}</p>
      <h2>Categoria</h2>
      <p>Categoria: ${med.section_name}${med.subsection_name ? ` - ${med.subsection_name}` : ''}</p>
    </div>`;

      customHtml = customHtml.replace('<div id="root"></div>', seoContent);

      // Schema.org específico do medicamento
      const schema = {
        "@context": "https://schema.org",
        "@type": "MedicalWebPage",
        "name": `${med.name} na Amamentação`,
        "description": description,
        "url": medUrl,
        "medicalAudience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde"
        },
        "about": {
          "@type": "Drug",
          "name": med.name,
          "medicalSpecialty": "Pediatria"
        },
        "publisher": {
          "@type": "Organization",
          "name": "PedBook",
          "url": "https://pedb.com.br"
        },
        "mainEntity": {
          "@type": "Drug",
          "name": med.name,
          "description": `Informações sobre ${med.name} durante a amamentação`
        }
      };

      const jsonLd = `<script type="application/ld+json">${JSON.stringify(schema, null, 2)}</script>`;
      customHtml = customHtml.replace('</head>', `  ${jsonLd}\n</head>`);

      fs.writeFileSync(path.join(medDir, 'index.html'), customHtml);
      console.log(`✅ Criado: /medicamentos-amamentacao/${med.slug}/index.html`);
      successCount++;

    } catch (err) {
      console.error(`❌ Erro ao gerar ${med.slug}:`, err.message);
    }
  }

  console.log(`🤱 Medicamentos na Amamentação: ${successCount} páginas geradas (${breastfeedingMedicationsData.length} principais + ${breastfeedingMedications.length} individuais)`);
  return successCount;
}

// Executar
async function main() {
  console.log('📊 Iniciando geração das NOVAS seções...\n');

  let totalPages = 0;

  // Gerar calculadoras
  totalPages += await generateCalculatorPages();

  // Gerar puericultura
  totalPages += await generatePuericulturePages();

  // Gerar intoxicações
  totalPages += await generatePoisoningPages();

  // Gerar fluxogramas
  totalPages += await generateFlowchartPages();

  // Gerar CID-10
  totalPages += await generateICDPages();

  // Gerar bulas profissionais
  totalPages += await generateProfessionalLabelPages();

  // Gerar medicamentos na amamentação
  totalPages += await generateBreastfeedingMedicationPages();

  console.log(`\n🎉 Total de páginas geradas: ${totalPages}`);
  console.log('✅ Geração das NOVAS seções concluída!');
}

/**
 * Gerar páginas HTML para intoxicações
 */
async function generatePoisoningPages() {
  console.log('\n☠️ Gerando páginas de intoxicações...');

  const distPath = path.join(process.cwd(), 'dist');
  const indexPath = path.join(distPath, 'index.html');

  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html não encontrado');
    return 0;
  }

  const baseHtml = fs.readFileSync(indexPath, 'utf8');
  let successCount = 0;

  for (const poison of poisoningsData) {
    try {
      const poisonDir = path.join(distPath, poison.slug);
      if (!fs.existsSync(poisonDir)) {
        fs.mkdirSync(poisonDir, { recursive: true });
      }

      const title = `${poison.name} | PedBook`;
      const description = poison.description;
      const keywords = poison.keywords;

      let customHtml = baseHtml;

      // Substituir meta tags
      customHtml = customHtml.replace(/<title>.*?<\/title>/, `<title>${title}</title>`);
      customHtml = customHtml.replace(/<meta name="description" content=".*?" ?\/>/, `<meta name="description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta name="keywords" content=".*?" ?\/>/, `<meta name="keywords" content="${keywords}" />`);

      // URL canônica
      const poisonUrl = `https://pedb.com.br/${poison.slug}`;
      customHtml = customHtml.replace(/<link rel="canonical" href=".*?" \/>/, `<link rel="canonical" href="${poisonUrl}" />`);

      // Open Graph
      customHtml = customHtml.replace(/<meta property="og:title" content=".*?" ?\/>/, `<meta property="og:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta property="og:description" content=".*?" ?\/>/, `<meta property="og:description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta property="og:url" content=".*?" ?\/>/, `<meta property="og:url" content="${poisonUrl}" />`);

      // Twitter Cards
      customHtml = customHtml.replace(/<meta name="twitter:title" content=".*?" ?\/>/, `<meta name="twitter:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta name="twitter:description" content=".*?" ?\/>/, `<meta name="twitter:description" content="${description}" />`);

      // Conteúdo SEO
      const seoContent = `
    <div id="root"></div>
    <!-- SEO Content for Crawlers -->
    <div style="display: none;" data-seo-content>
      <h1>${poison.name}</h1>
      <p>${poison.description}</p>
      <h2>Emergência Toxicológica</h2>
      <p>Protocolo de emergência para manejo de intoxicações em pediatria com antídotos específicos.</p>
      <h2>Antídotos e Tratamento</h2>
      <p>Informações sobre antídotos, doses pediátricas e manejo clínico especializado.</p>
    </div>`;

      customHtml = customHtml.replace('<div id="root"></div>', seoContent);

      // Schema.org
      const schema = {
        "@context": "https://schema.org",
        "@type": "MedicalWebPage",
        "name": poison.name,
        "description": description,
        "url": poisonUrl,
        "medicalAudience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde de Emergência"
        },
        "about": {
          "@type": "MedicalCondition",
          "name": poison.name,
          "medicalSpecialty": "Toxicologia"
        },
        "publisher": {
          "@type": "Organization",
          "name": "PedBook",
          "url": "https://pedb.com.br"
        }
      };

      const jsonLd = `<script type="application/ld+json">${JSON.stringify(schema, null, 2)}</script>`;
      customHtml = customHtml.replace('</head>', `  ${jsonLd}\n</head>`);

      fs.writeFileSync(path.join(poisonDir, 'index.html'), customHtml);
      console.log(`✅ Criado: /${poison.slug}/index.html`);
      successCount++;

    } catch (err) {
      console.error(`❌ Erro ao gerar ${poison.slug}:`, err.message);
    }
  }

  console.log(`☠️ Intoxicações: ${successCount}/${poisoningsData.length} páginas geradas`);
  return successCount;
}

/**
 * Gerar páginas HTML para fluxogramas
 */
async function generateFlowchartPages() {
  console.log('\n🌊 Gerando páginas de fluxogramas...');

  const distPath = path.join(process.cwd(), 'dist');
  const indexPath = path.join(distPath, 'index.html');

  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html não encontrado');
    return 0;
  }

  const baseHtml = fs.readFileSync(indexPath, 'utf8');
  let successCount = 0;

  for (const flow of flowchartsData) {
    try {
      const flowDir = path.join(distPath, flow.slug);
      if (!fs.existsSync(flowDir)) {
        fs.mkdirSync(flowDir, { recursive: true });
      }

      const title = `${flow.name} | PedBook`;
      const description = flow.description;
      const keywords = flow.keywords;

      let customHtml = baseHtml;

      // Substituir meta tags
      customHtml = customHtml.replace(/<title>.*?<\/title>/, `<title>${title}</title>`);
      customHtml = customHtml.replace(/<meta name="description" content=".*?" ?\/>/, `<meta name="description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta name="keywords" content=".*?" ?\/>/, `<meta name="keywords" content="${keywords}" />`);

      // URL canônica
      const flowUrl = `https://pedb.com.br/${flow.slug}`;
      customHtml = customHtml.replace(/<link rel="canonical" href=".*?" \/>/, `<link rel="canonical" href="${flowUrl}" />`);

      // Open Graph
      customHtml = customHtml.replace(/<meta property="og:title" content=".*?" ?\/>/, `<meta property="og:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta property="og:description" content=".*?" ?\/>/, `<meta property="og:description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta property="og:url" content=".*?" ?\/>/, `<meta property="og:url" content="${flowUrl}" />`);

      // Twitter Cards
      customHtml = customHtml.replace(/<meta name="twitter:title" content=".*?" ?\/>/, `<meta name="twitter:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta name="twitter:description" content=".*?" ?\/>/, `<meta name="twitter:description" content="${description}" />`);

      // Conteúdo SEO
      const seoContent = `
    <div id="root"></div>
    <!-- SEO Content for Crawlers -->
    <div style="display: none;" data-seo-content>
      <h1>${flow.name}</h1>
      <p>${flow.description}</p>
      <h2>Protocolo Clínico</h2>
      <p>Fluxograma baseado em evidências para diagnóstico e manejo clínico em pediatria.</p>
      <h2>Diretrizes Médicas</h2>
      <p>Protocolo estruturado para tomada de decisão clínica e manejo adequado.</p>
    </div>`;

      customHtml = customHtml.replace('<div id="root"></div>', seoContent);

      // Schema.org
      const schema = {
        "@context": "https://schema.org",
        "@type": "MedicalGuideline",
        "name": flow.name,
        "description": description,
        "url": flowUrl,
        "medicalAudience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde Pediátrica"
        },
        "about": {
          "@type": "MedicalCondition",
          "name": flow.name,
          "medicalSpecialty": "Pediatria"
        },
        "publisher": {
          "@type": "Organization",
          "name": "PedBook",
          "url": "https://pedb.com.br"
        }
      };

      const jsonLd = `<script type="application/ld+json">${JSON.stringify(schema, null, 2)}</script>`;
      customHtml = customHtml.replace('</head>', `  ${jsonLd}\n</head>`);

      fs.writeFileSync(path.join(flowDir, 'index.html'), customHtml);
      console.log(`✅ Criado: /${flow.slug}/index.html`);
      successCount++;

    } catch (err) {
      console.error(`❌ Erro ao gerar ${flow.slug}:`, err.message);
    }
  }

  console.log(`🌊 Fluxogramas: ${successCount}/${flowchartsData.length} páginas geradas`);
  return successCount;
}

/**
 * Gerar páginas HTML para CID-10
 */
async function generateICDPages() {
  console.log('\n🏥 Gerando páginas de CID-10...');

  const distPath = path.join(process.cwd(), 'dist');
  const indexPath = path.join(distPath, 'index.html');

  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html não encontrado');
    return 0;
  }

  const baseHtml = fs.readFileSync(indexPath, 'utf8');
  let successCount = 0;

  for (const icd of icdData) {
    try {
      const icdDir = path.join(distPath, icd.slug);
      if (!fs.existsSync(icdDir)) {
        fs.mkdirSync(icdDir, { recursive: true });
      }

      const title = `${icd.name} | PedBook`;
      const description = icd.description;
      const keywords = icd.keywords;

      let customHtml = baseHtml;

      // Substituir meta tags
      customHtml = customHtml.replace(/<title>.*?<\/title>/, `<title>${title}</title>`);
      customHtml = customHtml.replace(/<meta name="description" content=".*?" ?\/>/, `<meta name="description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta name="keywords" content=".*?" ?\/>/, `<meta name="keywords" content="${keywords}" />`);

      // URL canônica
      const icdUrl = `https://pedb.com.br/${icd.slug}`;
      customHtml = customHtml.replace(/<link rel="canonical" href=".*?" \/>/, `<link rel="canonical" href="${icdUrl}" />`);

      // Open Graph
      customHtml = customHtml.replace(/<meta property="og:title" content=".*?" ?\/>/, `<meta property="og:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta property="og:description" content=".*?" ?\/>/, `<meta property="og:description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta property="og:url" content=".*?" ?\/>/, `<meta property="og:url" content="${icdUrl}" />`);

      // Twitter Cards
      customHtml = customHtml.replace(/<meta name="twitter:title" content=".*?" ?\/>/, `<meta name="twitter:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta name="twitter:description" content=".*?" ?\/>/, `<meta name="twitter:description" content="${description}" />`);

      // Conteúdo SEO
      const seoContent = `
    <div id="root"></div>
    <!-- SEO Content for Crawlers -->
    <div style="display: none;" data-seo-content>
      <h1>${icd.name}</h1>
      <p>${icd.description}</p>
      <h2>Classificação Internacional de Doenças</h2>
      <p>Sistema de codificação médica para diagnósticos pediátricos conforme padrões internacionais.</p>
      <h2>Códigos Diagnósticos</h2>
      <p>Ferramenta para busca e consulta de códigos CID-10 específicos para pediatria.</p>
    </div>`;

      customHtml = customHtml.replace('<div id="root"></div>', seoContent);

      // Schema.org
      const schema = {
        "@context": "https://schema.org",
        "@type": "MedicalWebPage",
        "name": icd.name,
        "description": description,
        "url": icdUrl,
        "medicalAudience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde"
        },
        "about": {
          "@type": "MedicalCondition",
          "name": "Classificação de Doenças",
          "medicalSpecialty": "Medicina Geral"
        },
        "publisher": {
          "@type": "Organization",
          "name": "PedBook",
          "url": "https://pedb.com.br"
        }
      };

      const jsonLd = `<script type="application/ld+json">${JSON.stringify(schema, null, 2)}</script>`;
      customHtml = customHtml.replace('</head>', `  ${jsonLd}\n</head>`);

      fs.writeFileSync(path.join(icdDir, 'index.html'), customHtml);
      console.log(`✅ Criado: /${icd.slug}/index.html`);
      successCount++;

    } catch (err) {
      console.error(`❌ Erro ao gerar ${icd.slug}:`, err.message);
    }
  }

  console.log(`🏥 CID-10: ${successCount}/${icdData.length} páginas geradas`);
  return successCount;
}

/**
 * Gerar páginas HTML para bulas profissionais
 */
async function generateProfessionalLabelPages() {
  console.log('\n📋 Gerando páginas de bulas profissionais...');

  const distPath = path.join(process.cwd(), 'dist');
  const indexPath = path.join(distPath, 'index.html');

  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html não encontrado');
    return 0;
  }

  // Buscar bulas do Supabase
  const professionalLabels = await fetchProfessionalLabels();

  if (professionalLabels.length === 0) {
    console.log('⚠️ Nenhuma bula encontrada para gerar páginas');
    return 0;
  }

  const baseHtml = fs.readFileSync(indexPath, 'utf8');
  let successCount = 0;

  for (const label of professionalLabels) {
    try {
      const labelDir = path.join(distPath, 'bulas-profissionais', label.slug);
      if (!fs.existsSync(labelDir)) {
        fs.mkdirSync(labelDir, { recursive: true });
      }

      const title = `${label.title} - Bula Profissional | PedBook`;

      // Extrair informações do conteúdo da bula
      const content = label.content || '';

      // Extrair primeira linha como descrição
      const firstParagraph = content
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/##\./g, '') // Remove marcadores de seção
        .split('\n')
        .find(line => line.trim().length > 20) || '';

      const description = `${label.title}: bula profissional completa com informações técnicas, posologia, contraindicações e orientações para prescrição pediátrica. ${firstParagraph.substring(0, 100)}...`;

      // Keywords baseadas no nome do medicamento
      const medName = label.title.toLowerCase();
      const keywords = `${medName} bula, ${medName} profissional, bula ${medName}, posologia ${medName}, contraindicacoes ${medName}, bula completa, medicamento pediatrico`;

      let customHtml = baseHtml;

      // Substituir meta tags
      customHtml = customHtml.replace(/<title>.*?<\/title>/, `<title>${title}</title>`);
      customHtml = customHtml.replace(/<meta name="description" content=".*?" ?\/>/, `<meta name="description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta name="keywords" content=".*?" ?\/>/, `<meta name="keywords" content="${keywords}" />`);

      // URL canônica
      const labelUrl = `https://pedb.com.br/bulas-profissionais/${label.slug}`;
      customHtml = customHtml.replace(/<link rel="canonical" href=".*?" \/>/, `<link rel="canonical" href="${labelUrl}" />`);

      // Open Graph
      customHtml = customHtml.replace(/<meta property="og:title" content=".*?" ?\/>/, `<meta property="og:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta property="og:description" content=".*?" ?\/>/, `<meta property="og:description" content="${description}" />`);
      customHtml = customHtml.replace(/<meta property="og:url" content=".*?" ?\/>/, `<meta property="og:url" content="${labelUrl}" />`);

      // Twitter Cards
      customHtml = customHtml.replace(/<meta name="twitter:title" content=".*?" ?\/>/, `<meta name="twitter:title" content="${title}" />`);
      customHtml = customHtml.replace(/<meta name="twitter:description" content=".*?" ?\/>/, `<meta name="twitter:description" content="${description}" />`);

      // Extrair seções principais do conteúdo para SEO
      const sections = content.split('##.').filter(section => section.trim().length > 0);
      const seoSections = sections.slice(0, 5).map((section, index) => {
        const lines = section.split('\n');
        const sectionTitle = lines[0]?.trim() || `Seção ${index + 1}`;
        const sectionContent = lines.slice(1).join(' ').replace(/<[^>]*>/g, '').substring(0, 200);
        return `<h3>${sectionTitle}</h3><p>${sectionContent}...</p>`;
      }).join('\n      ');

      // Conteúdo SEO
      const seoContent = `
    <div id="root"></div>
    <!-- SEO Content for Crawlers -->
    <div style="display: none;" data-seo-content>
      <h1>${label.title} - Bula Profissional</h1>
      <p>Bula profissional completa de ${label.title} com informações técnicas detalhadas para prescrição pediátrica.</p>
      <h2>Informações Técnicas</h2>
      <p>Consulte a bula profissional de ${label.title} para informações sobre posologia, contraindicações, interações medicamentosas e orientações específicas para uso em pediatria.</p>
      ${seoSections}
      <h2>Prescrição Pediátrica</h2>
      <p>Informações específicas para prescrição de ${label.title} em crianças e adolescentes, incluindo doses por peso e idade.</p>
    </div>`;

      customHtml = customHtml.replace('<div id="root"></div>', seoContent);

      // Schema.org
      const schema = {
        "@context": "https://schema.org",
        "@type": "MedicalWebPage",
        "name": `${label.title} - Bula Profissional`,
        "description": description,
        "url": labelUrl,
        "medicalAudience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde"
        },
        "about": {
          "@type": "Drug",
          "name": label.title,
          "medicalSpecialty": "Pediatria"
        },
        "publisher": {
          "@type": "Organization",
          "name": "PedBook",
          "url": "https://pedb.com.br"
        },
        "mainEntity": {
          "@type": "Drug",
          "name": label.title,
          "description": `Bula profissional de ${label.title}`,
          "medicalSpecialty": "Pediatria"
        }
      };

      const jsonLd = `<script type="application/ld+json">${JSON.stringify(schema, null, 2)}</script>`;
      customHtml = customHtml.replace('</head>', `  ${jsonLd}\n</head>`);

      fs.writeFileSync(path.join(labelDir, 'index.html'), customHtml);
      console.log(`✅ Criado: /bulas-profissionais/${label.slug}/index.html`);
      successCount++;

    } catch (err) {
      console.error(`❌ Erro ao gerar ${label.slug}:`, err.message);
    }
  }

  console.log(`📋 Bulas Profissionais: ${successCount}/${professionalLabels.length} páginas geradas`);
  return successCount;
}

main().catch(console.error);
